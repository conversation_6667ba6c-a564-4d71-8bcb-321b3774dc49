{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest"], "env": {"SUPABASE_URL": "https://xptegoszrnglzvfvvypq.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwdGVnb3N6cm5nbHp2ZnZ2eXBxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MDU4NDcsImV4cCI6MjA2OTI4MTg0N30.VJBAOl8jkFbnnkjO9MtsDPviQGjOd9yIXS_2flY19wg", "SUPABASE_ACCESS_TOKEN": "********************************************", "FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["supabase_select", "supabase_insert", "supabase_update", "supabase_delete", "supabase_rpc", "execute_sql", "list_tables"]}}}