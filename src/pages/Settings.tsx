import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { useLanguage } from '@/contexts/LanguageContext';
import { useToast } from '@/hooks/use-toast';
import { User, Bell, Shield, Globe, Palette, Save } from 'lucide-react';

const Settings = () => {
  const { user, userData } = useAuthContext();
  const { language, setLanguage, t } = useLanguage();
  const { toast } = useToast();
  
  const [settings, setSettings] = useState({
    // Profile Settings
    displayName: userData?.name || '',
    email: userData?.email || '',
    organization: userData?.organization || '',
    
    // Notification Settings
    emailNotifications: true,
    pushNotifications: true,
    problemUpdates: true,
    solutionUpdates: true,
    weeklyDigest: false,
    
    // Privacy Settings
    profileVisibility: 'public',
    showEmail: false,
    showOrganization: true,
    
    // Interface Settings
    theme: 'light',
    language: language,
    timezone: 'Asia/Damascus'
  });

  const handleSave = () => {
    // Here you would save settings to the database
    toast({
      title: t('common.success'),
      description: "تم حفظ الإعدادات بنجاح",
    });
  };

  const handleLanguageChange = (newLanguage: 'ar' | 'en') => {
    setLanguage(newLanguage);
    setSettings(prev => ({ ...prev, language: newLanguage }));
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">{t('nav.settings')}</h1>
          <p className="text-gray-600 mt-2">إدارة إعدادات حسابك وتفضيلاتك</p>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="w-4 h-4" />
              الملف الشخصي
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="w-4 h-4" />
              الإشعارات
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              الخصوصية
            </TabsTrigger>
            <TabsTrigger value="interface" className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              الواجهة
            </TabsTrigger>
          </TabsList>

          {/* Profile Settings */}
          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الملف الشخصي</CardTitle>
                <CardDescription>
                  قم بتحديث معلومات ملفك الشخصي
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="displayName">الاسم المعروض</Label>
                    <Input
                      id="displayName"
                      value={settings.displayName}
                      onChange={(e) => setSettings(prev => ({ ...prev, displayName: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input
                      id="email"
                      type="email"
                      value={settings.email}
                      onChange={(e) => setSettings(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="organization">المؤسسة/الوزارة</Label>
                  <Input
                    id="organization"
                    value={settings.organization}
                    onChange={(e) => setSettings(prev => ({ ...prev, organization: e.target.value }))}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notification Settings */}
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الإشعارات</CardTitle>
                <CardDescription>
                  اختر كيف ومتى تريد تلقي الإشعارات
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>إشعارات البريد الإلكتروني</Label>
                    <p className="text-sm text-gray-500">تلقي إشعارات عبر البريد الإلكتروني</p>
                  </div>
                  <Switch
                    checked={settings.emailNotifications}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, emailNotifications: checked }))}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>إشعارات المتصفح</Label>
                    <p className="text-sm text-gray-500">تلقي إشعارات فورية في المتصفح</p>
                  </div>
                  <Switch
                    checked={settings.pushNotifications}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, pushNotifications: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>تحديثات المشاكل</Label>
                    <p className="text-sm text-gray-500">إشعارات عند تحديث المشاكل التي تتابعها</p>
                  </div>
                  <Switch
                    checked={settings.problemUpdates}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, problemUpdates: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>تحديثات الحلول</Label>
                    <p className="text-sm text-gray-500">إشعارات عند إضافة حلول جديدة</p>
                  </div>
                  <Switch
                    checked={settings.solutionUpdates}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, solutionUpdates: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>النشرة الأسبوعية</Label>
                    <p className="text-sm text-gray-500">ملخص أسبوعي بأهم المستجدات</p>
                  </div>
                  <Switch
                    checked={settings.weeklyDigest}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, weeklyDigest: checked }))}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Privacy Settings */}
          <TabsContent value="privacy">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الخصوصية</CardTitle>
                <CardDescription>
                  تحكم في من يمكنه رؤية معلوماتك
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>مستوى ظهور الملف الشخصي</Label>
                  <Select 
                    value={settings.profileVisibility} 
                    onValueChange={(value) => setSettings(prev => ({ ...prev, profileVisibility: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">عام - يمكن للجميع رؤيته</SelectItem>
                      <SelectItem value="registered">المسجلين فقط</SelectItem>
                      <SelectItem value="private">خاص - مخفي</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>إظهار البريد الإلكتروني</Label>
                    <p className="text-sm text-gray-500">السماح للآخرين برؤية بريدك الإلكتروني</p>
                  </div>
                  <Switch
                    checked={settings.showEmail}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, showEmail: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>إظهار المؤسسة</Label>
                    <p className="text-sm text-gray-500">السماح للآخرين برؤية مؤسستك</p>
                  </div>
                  <Switch
                    checked={settings.showOrganization}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, showOrganization: checked }))}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Interface Settings */}
          <TabsContent value="interface">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الواجهة</CardTitle>
                <CardDescription>
                  خصص مظهر وسلوك المنصة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>اللغة</Label>
                  <Select 
                    value={settings.language} 
                    onValueChange={handleLanguageChange}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ar">العربية</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>المظهر</Label>
                  <Select 
                    value={settings.theme} 
                    onValueChange={(value) => setSettings(prev => ({ ...prev, theme: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">فاتح</SelectItem>
                      <SelectItem value="dark">داكن</SelectItem>
                      <SelectItem value="system">تلقائي حسب النظام</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>المنطقة الزمنية</Label>
                  <Select 
                    value={settings.timezone} 
                    onValueChange={(value) => setSettings(prev => ({ ...prev, timezone: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Asia/Damascus">دمشق (GMT+3)</SelectItem>
                      <SelectItem value="Europe/London">لندن (GMT+0)</SelectItem>
                      <SelectItem value="America/New_York">نيويورك (GMT-5)</SelectItem>
                      <SelectItem value="Asia/Dubai">دبي (GMT+4)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Save Button */}
        <div className="flex justify-end pt-6">
          <Button onClick={handleSave} className="flex items-center gap-2">
            <Save className="w-4 h-4" />
            حفظ الإعدادات
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default Settings;