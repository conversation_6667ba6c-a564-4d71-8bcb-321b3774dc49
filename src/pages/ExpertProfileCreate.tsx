import { useNavigate } from 'react-router-dom'
import { Award } from 'lucide-react'
import { Link } from 'react-router-dom'
import { ExpertProfileForm } from '@/components/experts/ExpertProfileForm'
import { useAuth } from '@/hooks/useAuth'
import { useEffect } from 'react'

const ExpertProfileCreate = () => {
  const navigate = useNavigate()
  const { user, loading } = useAuth()

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth/login')
    }
  }, [user, loading, navigate])

  const handleSuccess = () => {
    navigate('/experts/dashboard')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                <Award className="w-6 h-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">إنشاء ملف الخبير</h1>
                <p className="text-sm text-gray-600">انضم إلى شبكة الخبراء التقنيين</p>
              </div>
            </Link>
            <Link to="/experts" className="text-blue-600 hover:text-blue-800">
              العودة لدليل الخبراء
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ExpertProfileForm onSuccess={handleSuccess} />
      </div>
    </div>
  )
}

export default ExpertProfileCreate