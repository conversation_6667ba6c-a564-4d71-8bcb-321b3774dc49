import { useState, useEffect } from 'react'
import { useSearchParams, Link } from 'react-router-dom'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { EnhancedSearchInterface } from '@/components/search/EnhancedSearchInterface'
import { SearchResultsDisplay } from '@/components/search/SearchResultsDisplay'
import { searchService } from '@/lib/search'
import { 
  SearchResults as SearchResultsType,
  SearchFilters,
  SortOption,
  SearchQuery,
  urlParamsToFilters,
  filtersToUrlParams
} from '@/lib/search'
import { useSearchAnalytics } from '@/lib/search/SearchAnalytics'
import { 
  Search, 
  Loader2
} from 'lucide-react'

const SearchResults = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const { toast } = useToast()
  const { trackSearch } = useSearchAnalytics()
  
  // Search state
  const [results, setResults] = useState<SearchResultsType | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [analyticsId, setAnalyticsId] = useState<string | null>(null)
  
  // Parse URL parameters
  const urlState = urlParamsToFilters(searchParams)
  const [query, setQuery] = useState(urlState.query)
  const [filters, setFilters] = useState<SearchFilters>(urlState.filters)
  const [sortBy, setSortBy] = useState<SortOption>(urlState.sortBy)

  // Perform search when URL parameters change
  useEffect(() => {
    const newUrlState = urlParamsToFilters(searchParams)
    
    // Update state if URL changed
    if (newUrlState.query !== query) {
      setQuery(newUrlState.query)
    }
    if (JSON.stringify(newUrlState.filters) !== JSON.stringify(filters)) {
      setFilters(newUrlState.filters)
    }
    if (newUrlState.sortBy !== sortBy) {
      setSortBy(newUrlState.sortBy)
    }
    
    // Perform search if we have a query
    if (newUrlState.query.trim()) {
      performSearch(newUrlState.query, newUrlState.filters, newUrlState.sortBy, 1)
    }
  }, [searchParams])

  const performSearch = async (
    searchQuery: string, 
    searchFilters: SearchFilters, 
    searchSortBy: SortOption,
    page: number = currentPage
  ) => {
    if (!searchQuery.trim()) return

    setLoading(true)
    setError(null)
    
    try {
      const searchRequest: SearchQuery = {
        text: searchQuery.trim(),
        filters: searchFilters,
        sortBy: searchSortBy,
        pagination: {
          limit: pageSize,
          offset: (page - 1) * pageSize
        },
        language: 'auto'
      }

      const searchResults = await searchService.search(searchRequest)
      setResults(searchResults)
      setCurrentPage(page)
      
      // Track search analytics using the new service
      try {
        const analyticsId = await trackSearch(searchRequest, searchResults)
        setAnalyticsId(analyticsId)
      } catch (analyticsError) {
        console.warn('Failed to track search analytics:', analyticsError)
      }
      
    } catch (error) {
      console.error('Search error:', error)
      setError('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.')
      setResults(null)
      
      toast({
        title: 'خطأ في البحث',
        description: 'حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (searchQuery: string, searchFilters: SearchFilters, searchSortBy: SortOption) => {
    // Update URL with new search parameters
    const params = filtersToUrlParams(searchFilters, searchQuery, searchSortBy)
    setSearchParams(params)
  }

  const handleSortChange = (newSortBy: SortOption) => {
    setSortBy(newSortBy)
    const params = filtersToUrlParams(filters, query, newSortBy)
    setSearchParams(params)
  }

  const handlePageChange = (page: number) => {
    if (query.trim()) {
      performSearch(query, filters, sortBy, page)
    }
  }

  const handleResultClick = (result: any) => {
    // Result click tracking is now handled in SearchResultsDisplay component
    // This function can be used for navigation or other actions
    console.log('Result clicked:', result)
  }

  // Helper functions are now handled by the SearchAnalyticsService

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                <Search className="w-6 h-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">نتائج البحث</h1>
                <p className="text-sm text-gray-600">البحث في المنصة</p>
              </div>
            </Link>
            <Link to="/" className="text-blue-600 hover:text-blue-800">
              العودة للصفحة الرئيسية
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Enhanced Search Interface */}
        <div className="mb-8">
          <EnhancedSearchInterface
            onSearch={handleSearch}
            initialQuery={query}
            initialFilters={filters}
            initialSortBy={sortBy}
            showFilters={true}
            placeholder="ابحث في المشاكل، الخبراء، والحلول..."
          />
        </div>

        {/* Loading State */}
        {loading && !results && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600 mr-3" />
            <span className="text-gray-600">جاري البحث...</span>
          </div>
        )}

        {/* Search Results */}
        {!loading && (query.trim() || results) && (
          <SearchResultsDisplay
            results={results}
            loading={loading}
            error={error}
            onSortChange={handleSortChange}
            onPageChange={handlePageChange}
            onResultClick={handleResultClick}
            currentPage={currentPage}
            pageSize={pageSize}
            analyticsId={analyticsId || undefined}
          />
        )}

        {/* Welcome message when no search performed */}
        {!loading && !query.trim() && !results && (
          <Card>
            <CardContent className="p-12 text-center">
              <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                مرحباً بك في البحث المتقدم
              </h3>
              <p className="text-gray-600 mb-6">
                استخدم البحث أعلاه للعثور على المشاكل والخبراء والحلول
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Search className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <h4 className="font-medium text-blue-900 mb-1">بحث ذكي</h4>
                  <p className="text-sm text-blue-700">
                    بحث متقدم مع اقتراحات تلقائية
                  </p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <Search className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <h4 className="font-medium text-green-900 mb-1">فلاتر متقدمة</h4>
                  <p className="text-sm text-green-700">
                    تصفية النتائج حسب النوع والقطاع
                  </p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <Search className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <h4 className="font-medium text-purple-900 mb-1">نتائج شاملة</h4>
                  <p className="text-sm text-purple-700">
                    البحث في جميع أنواع المحتوى
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

export default SearchResults