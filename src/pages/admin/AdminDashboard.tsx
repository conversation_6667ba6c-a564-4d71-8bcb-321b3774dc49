import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { Layout } from '@/components/layout/Layout'
import { ProtectedRoute } from '@/components/auth/AuthProvider'
import { DashboardSearchWidget } from '@/components/search/DashboardSearchWidget'
import { 
  Users, 
  FileText, 
  MessageSquare, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Shield,
  Settings,
  Search
} from 'lucide-react'
import { 
  LazyAdminStatsWrapper as AdminStats,
  LazyContentModerationWrapper as ContentModeration,
  LazyUserManagementWrapper as UserManagement,
  LazySystemSettingsWrapper as SystemSettings,
  LazyAnalyticsDashboardWrapper as AnalyticsDashboard
} from '@/components/lazy/LazyAdminComponents'

export default function AdminDashboard() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState('overview')

  return (
    <ProtectedRoute requiredRole="admin">
      <Layout>
        <div className="min-h-screen bg-gray-50" dir="rtl">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Header */}
            <div className="mb-8">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                    <Shield className="w-8 h-8 text-blue-600" />
                    لوحة التحكم الإدارية
                  </h1>
                  <p className="text-gray-600 mt-2">
                    إدارة المحتوى والمستخدمين ومراقبة أداء المنصة
                  </p>
                </div>
                
                {/* Admin Search Widget */}
                <div className="lg:max-w-md">
                  <DashboardSearchWidget 
                    placeholder="البحث في لوحة التحكم..."
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <AdminStats />

            {/* Main Content Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  نظرة عامة
                </TabsTrigger>
                <TabsTrigger value="content" className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  إدارة المحتوى
                </TabsTrigger>
                <TabsTrigger value="users" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  إدارة المستخدمين
                </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  التحليلات
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  الإعدادات
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Recent Activity */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        النشاط الأخير
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <FileText className="w-4 h-4 text-blue-600" />
                            <div>
                              <p className="font-medium">مشكلة جديدة مطروحة</p>
                              <p className="text-sm text-gray-600">تطوير نظام إدارة المستشفيات</p>
                            </div>
                          </div>
                          <Badge variant="secondary">منذ 5 دقائق</Badge>
                        </div>
                        
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <Users className="w-4 h-4 text-green-600" />
                            <div>
                              <p className="font-medium">خبير جديد انضم</p>
                              <p className="text-sm text-gray-600">د. أحمد الخطيب - أمن المعلومات</p>
                            </div>
                          </div>
                          <Badge variant="secondary">منذ 15 دقيقة</Badge>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <MessageSquare className="w-4 h-4 text-yellow-600" />
                            <div>
                              <p className="font-medium">حل جديد مقترح</p>
                              <p className="text-sm text-gray-600">حل لمشكلة أتمتة العمليات الجمركية</p>
                            </div>
                          </div>
                          <Badge variant="secondary">منذ 30 دقيقة</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Pending Actions */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="w-5 h-5 text-orange-500" />
                        الإجراءات المطلوبة
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 border border-orange-200 rounded-lg">
                          <div>
                            <p className="font-medium text-orange-800">3 مشاكل تحتاج مراجعة</p>
                            <p className="text-sm text-orange-600">مشاكل مبلغ عنها من المستخدمين</p>
                          </div>
                          <Button size="sm" variant="outline">
                            مراجعة
                          </Button>
                        </div>

                        <div className="flex items-center justify-between p-3 border border-blue-200 rounded-lg">
                          <div>
                            <p className="font-medium text-blue-800">5 خبراء في انتظار التفعيل</p>
                            <p className="text-sm text-blue-600">طلبات انضمام جديدة</p>
                          </div>
                          <Button size="sm" variant="outline">
                            مراجعة
                          </Button>
                        </div>

                        <div className="flex items-center justify-between p-3 border border-green-200 rounded-lg">
                          <div>
                            <p className="font-medium text-green-800">12 حل تم تقييمه إيجابياً</p>
                            <p className="text-sm text-green-600">حلول جاهزة للنشر</p>
                          </div>
                          <Button size="sm" variant="outline">
                            نشر
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="content">
                <ContentModeration />
              </TabsContent>

              <TabsContent value="users">
                <UserManagement />
              </TabsContent>

              <TabsContent value="analytics">
                <AnalyticsDashboard />
              </TabsContent>

              <TabsContent value="settings">
                <SystemSettings />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  )
}