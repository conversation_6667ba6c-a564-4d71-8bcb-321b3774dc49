import React from 'react'
import { LoginForm } from '@/components/auth/LoginForm'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useAuthContext } from '@/components/auth/AuthProvider'
import { useEffect } from 'react'

export default function Login() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { user } = useAuthContext()
  const redirectTo = searchParams.get('redirect') || '/'

  // Redirect if already authenticated
  useEffect(() => {
    if (user) {
      navigate(redirectTo, { replace: true })
    }
  }, [user, navigate, redirectTo])

  const handleSuccess = () => {
    navigate(redirectTo, { replace: true })
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            مركز سوريا الذكي
          </h1>
          <p className="text-gray-600">
            منصة الحلول التقنية للمؤسسات الحكومية
          </p>
        </div>
        
        <LoginForm onSuccess={handleSuccess} />
      </div>
    </div>
  )
}