/**
 * User-related type definitions for the Syrian Technical Solutions Platform
 */

// User role enumeration
export type UserRole = 'expert' | 'ministry_user' | 'admin';

// User profile interface
export interface UserProfile {
  avatar_url?: string;
  bio?: string;
  expertise_areas: string[];
  location?: string;
  phone?: string;
  organization?: string;
  position?: string;
  languages: string[];
}

// Core user data interface
export interface UserData {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  bio?: string;
  location: string;
  phone_number?: string;
  organization?: string;
  position?: string;
  languages: string[];
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}

// Expert-specific data interface
export interface ExpertData {
  id: string;
  user_id: string;
  expertise_areas: ExpertiseArea[];
  experience_years: number;
  availability: ExpertAvailability;
  rating: number;
  total_contributions: number;
  success_rate: number;
  response_time_hours: number;
  portfolio: PortfolioItem[];
  certifications: Certification[];
  is_verified: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  // Joined user data
  users?: UserData;
}

// Expert availability status
export type ExpertAvailability = 'available' | 'busy' | 'unavailable';

// Expertise area interface
export interface ExpertiseArea {
  category: string;
  subcategory?: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
}

// Portfolio item interface
export interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  url?: string;
  image_url?: string;
  technologies: string[];
  created_at: string;
}

// Certification interface
export interface Certification {
  id: string;
  name: string;
  issuer: string;
  issue_date: string;
  expiry_date?: string;
  credential_id?: string;
  credential_url?: string;
}

// User registration data
export interface UserRegistrationData {
  email: string;
  password: string;
  name: string;
  role: UserRole;
  location?: string;
  organization?: string;
  position?: string;
  phone_number?: string;
  bio?: string;
}

// User update data
export interface UserUpdateData {
  name?: string;
  bio?: string;
  location?: string;
  phone_number?: string;
  organization?: string;
  position?: string;
  languages?: string[];
  avatar?: string;
}

// Expert profile creation data
export interface ExpertProfileData {
  user_id: string;
  expertise_areas: ExpertiseArea[];
  experience_years: number;
  availability: ExpertAvailability;
  portfolio?: PortfolioItem[];
  certifications?: Certification[];
}

// Expert profile update data
export interface ExpertProfileUpdateData {
  expertise_areas?: ExpertiseArea[];
  experience_years?: number;
  availability?: ExpertAvailability;
  portfolio?: PortfolioItem[];
  certifications?: Certification[];
  bio?: string;
}

// User filters for queries
export interface UserFilters {
  role?: UserRole;
  location?: string;
  organization?: string;
  is_active?: boolean;
  includeDeleted?: boolean;
}

// Expert filters for queries
export interface ExpertFilters {
  availability?: ExpertAvailability;
  expertise?: string;
  location?: string;
  minRating?: number;
  minExperience?: number;
  includeDeleted?: boolean;
}

