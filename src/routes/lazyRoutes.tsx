import { createLazyComponent, lazyConfigs } from '@/utils/lazyLoad';

/**
 * Lazy-loaded route components with optimized loading strategies
 * Components are grouped by usage frequency and importance
 */

// Critical routes - loaded with minimal delay
export const Index = createLazyComponent(
  () => import('@/pages/Index'),
  lazyConfigs.critical
);

export const Login = createLazyComponent(
  () => import('@/pages/auth/Login'),
  lazyConfigs.critical
);

export const Register = createLazyComponent(
  () => import('@/pages/auth/Register'),
  lazyConfigs.critical
);

// Standard routes - main application pages
export const Problems = createLazyComponent(
  () => import('@/pages/Problems'),
  lazyConfigs.standard
);

export const ProblemDetail = createLazyComponent(
  () => import('@/pages/ProblemDetail'),
  lazyConfigs.standard
);

export const ProblemSubmit = createLazyComponent(
  () => import('@/pages/ProblemSubmit'),
  lazyConfigs.standard
);

export const Experts = createLazyComponent(
  () => import('@/pages/Experts'),
  lazyConfigs.standard
);

export const ExpertProfile = createLazyComponent(
  () => import('@/pages/ExpertProfile'),
  lazyConfigs.standard
);

export const ExpertProfileCreate = createLazyComponent(
  () => import('@/pages/ExpertProfileCreate'),
  lazyConfigs.standard
);

export const ExpertDashboardPage = createLazyComponent(
  () => import('@/pages/ExpertDashboardPage'),
  lazyConfigs.standard
);

export const SearchResults = createLazyComponent(
  () => import('@/pages/SearchResults'),
  lazyConfigs.standard
);

// User-specific routes
export const Profile = createLazyComponent(
  () => import('@/pages/Profile'),
  lazyConfigs.standard
);

export const Settings = createLazyComponent(
  () => import('@/pages/Settings'),
  lazyConfigs.standard
);

// Less frequent routes
export const Submit = createLazyComponent(
  () => import('@/pages/Submit'),
  lazyConfigs.deferred
);

export const EntryDetails = createLazyComponent(
  () => import('@/pages/EntryDetails'),
  lazyConfigs.deferred
);

export const ForgotPassword = createLazyComponent(
  () => import('@/pages/auth/ForgotPassword'),
  lazyConfigs.deferred
);

// Admin routes - loaded with delay since they're less frequently accessed
export const AdminPanel = createLazyComponent(
  () => import('@/pages/AdminPanel'),
  lazyConfigs.deferred
);

export const AdminDashboard = createLazyComponent(
  () => import('@/pages/admin/AdminDashboard'),
  lazyConfigs.deferred
);

export const AdminUsers = createLazyComponent(
  () => import('@/pages/AdminUsers'),
  lazyConfigs.deferred
);

export const AdminAnalytics = createLazyComponent(
  () => import('@/pages/AdminAnalytics'),
  lazyConfigs.deferred
);

// Development/Testing routes
export const SupabaseTest = createLazyComponent(
  () => import('@/components/SupabaseTest').then(module => ({
    default: module.default || module.SupabaseTest
  })),
  lazyConfigs.deferred
);

// Error pages
export const NotFound = createLazyComponent(
  () => import('@/pages/NotFound'),
  lazyConfigs.standard
);

/**
 * Preload strategies for related components
 */
export const preloadStrategies = {
  // Preload authentication pages when on landing page
  authPages: () => {
    (Login as any).preload?.();
    (Register as any).preload?.();
  },
  
  // Preload main app pages after authentication
  mainPages: () => {
    (Problems as any).preload?.();
    (Experts as any).preload?.();
    (SearchResults as any).preload?.();
  },
  
  // Preload problem-related pages when viewing problems
  problemPages: () => {
    (ProblemDetail as any).preload?.();
    (ProblemSubmit as any).preload?.();
  },
  
  // Preload expert-related pages when viewing experts
  expertPages: () => {
    (ExpertProfile as any).preload?.();
    (ExpertProfileCreate as any).preload?.();
    (ExpertDashboardPage as any).preload?.();
  },
  
  // Preload admin pages when accessing admin area
  adminPages: () => {
    (AdminDashboard as any).preload?.();
    (AdminUsers as any).preload?.();
    (AdminAnalytics as any).preload?.();
  },
};