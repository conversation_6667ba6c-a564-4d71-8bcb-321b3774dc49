import { QueryClient, QueryCache, MutationCache } from '@tanstack/react-query';
import { toast } from 'sonner';

// Cache configuration constants
export const CACHE_TIMES = {
  // Static data that rarely changes
  STATIC: 1000 * 60 * 60 * 24, // 24 hours
  // User-specific data
  USER: 1000 * 60 * 30, // 30 minutes
  // Dynamic content that changes frequently
  DYNAMIC: 1000 * 60 * 5, // 5 minutes
  // Real-time data
  REALTIME: 1000 * 60, // 1 minute
  // Search results
  SEARCH: 1000 * 60 * 10, // 10 minutes
} as const;

export const STALE_TIMES = {
  STATIC: 1000 * 60 * 60 * 12, // 12 hours
  USER: 1000 * 60 * 15, // 15 minutes
  DYNAMIC: 1000 * 60 * 2, // 2 minutes
  REALTIME: 1000 * 30, // 30 seconds
  SEARCH: 1000 * 60 * 5, // 5 minutes
} as const;

// Query key factories for consistent cache management
export const queryKeys = {
  // User-related queries
  user: {
    all: ['users'] as const,
    profile: (id: string) => [...queryKeys.user.all, 'profile', id] as const,
    preferences: (id: string) => [...queryKeys.user.all, 'preferences', id] as const,
    activity: (id: string) => [...queryKeys.user.all, 'activity', id] as const,
  },
  
  // Problem-related queries
  problems: {
    all: ['problems'] as const,
    lists: () => [...queryKeys.problems.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.problems.lists(), filters] as const,
    details: () => [...queryKeys.problems.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.problems.details(), id] as const,
    solutions: (id: string) => [...queryKeys.problems.detail(id), 'solutions'] as const,
    comments: (id: string) => [...queryKeys.problems.detail(id), 'comments'] as const,
  },
  
  // Expert-related queries
  experts: {
    all: ['experts'] as const,
    lists: () => [...queryKeys.experts.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.experts.lists(), filters] as const,
    details: () => [...queryKeys.experts.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.experts.details(), id] as const,
    reviews: (id: string) => [...queryKeys.experts.detail(id), 'reviews'] as const,
    availability: (id: string) => [...queryKeys.experts.detail(id), 'availability'] as const,
  },
  
  // Search-related queries
  search: {
    all: ['search'] as const,
    results: (query: string, filters: Record<string, any>) => 
      [...queryKeys.search.all, 'results', query, filters] as const,
    suggestions: (query: string) => [...queryKeys.search.all, 'suggestions', query] as const,
    analytics: () => [...queryKeys.search.all, 'analytics'] as const,
  },
  
  // System-related queries
  system: {
    all: ['system'] as const,
    config: () => [...queryKeys.system.all, 'config'] as const,
    stats: () => [...queryKeys.system.all, 'stats'] as const,
    health: () => [...queryKeys.system.all, 'health'] as const,
  },
} as const;

// Error handling for queries
const handleQueryError = (error: Error, query: { state: { fetchStatus: string; data?: unknown }; queryKey: unknown[] }) => {
  console.error('Query error:', error, query);
  
  // Don't show toast for background refetch errors
  if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {
    return;
  }
  
  // Show user-friendly error messages
  const errorMessage = error.message || 'Something went wrong. Please try again.';
  toast.error(errorMessage);
};

// Error handling for mutations
const handleMutationError = (error: Error, variables: unknown, context: unknown, mutation: { mutationKey?: unknown[] }) => {
  console.error('Mutation error:', error, { variables, context, mutation });
  
  const errorMessage = error.message || 'Failed to save changes. Please try again.';
  toast.error(errorMessage);
};

// Create query cache with error handling
const queryCache = new QueryCache({
  onError: handleQueryError,
});

// Create mutation cache with error handling
const mutationCache = new MutationCache({
  onError: handleMutationError,
});

// Default query options based on data type
export const getDefaultQueryOptions = (type: keyof typeof CACHE_TIMES) => ({
  staleTime: STALE_TIMES[type],
  cacheTime: CACHE_TIMES[type],
  retry: (failureCount: number, error: { status?: number }) => {
    // Don't retry on 4xx errors (client errors)
    if (error?.status >= 400 && error?.status < 500) {
      return false;
    }
    // Retry up to 3 times for other errors
    return failureCount < 3;
  },
  retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
});

// Create the main query client
export const queryClient = new QueryClient({
  queryCache,
  mutationCache,
  defaultOptions: {
    queries: {
      ...getDefaultQueryOptions('DYNAMIC'),
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: true,
      // Enable background refetch for better UX
      refetchInterval: false,
      // Network mode for offline support
      networkMode: 'online',
    },
    mutations: {
      retry: 1,
      networkMode: 'online',
      onSuccess: () => {
        // Optionally show success message
        // toast.success('Changes saved successfully');
      },
    },
  },
});

// Cache invalidation helpers
export const invalidateQueries = {
  user: (userId?: string) => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.user.preferences(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.user.activity(userId) });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.all });
    }
  },
  
  problems: (problemId?: string) => {
    if (problemId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.detail(problemId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.solutions(problemId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.comments(problemId) });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.all });
    }
  },
  
  experts: (expertId?: string) => {
    if (expertId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.detail(expertId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.reviews(expertId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.availability(expertId) });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.all });
    }
  },
  
  search: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.search.all });
  },
  
  all: () => {
    queryClient.invalidateQueries();
  },
};

// Cache prefetching helpers
export const prefetchQueries = {
  userProfile: async (userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.user.profile(userId),
      queryFn: () => fetch(`/api/users/${userId}`).then(res => res.json()),
      ...getDefaultQueryOptions('USER'),
    });
  },
  
  problemDetails: async (problemId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.problems.detail(problemId),
      queryFn: () => fetch(`/api/problems/${problemId}`).then(res => res.json()),
      ...getDefaultQueryOptions('DYNAMIC'),
    });
  },
  
  expertDetails: async (expertId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.experts.detail(expertId),
      queryFn: () => fetch(`/api/experts/${expertId}`).then(res => res.json()),
      ...getDefaultQueryOptions('USER'),
    });
  },
};

// Cache persistence configuration (for offline support)
export const persistOptions = {
  maxAge: 1000 * 60 * 60 * 24 * 7, // 7 days
  buster: 'v1', // Increment to invalidate all persisted cache
};

export default queryClient;