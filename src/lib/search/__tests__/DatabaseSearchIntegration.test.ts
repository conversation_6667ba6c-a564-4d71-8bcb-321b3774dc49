import { describe, it, expect, beforeEach, vi } from 'vitest'
import { SearchService } from '../SearchService'
import { SearchQuery, SearchFilters } from '../types'

// Mock the database module
vi.mock('@/lib/database', () => ({
  supabase: {
    rpc: vi.fn(),
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => ({
            limit: vi.fn(() => ({
              data: [],
              error: null
            }))
          }))
        }))
      }))
    }))
  }
}))

describe('Database Search Integration', () => {
  let searchService: SearchService

  beforeEach(() => {
    searchService = new SearchService()
    vi.clearAllMocks()
  })

  describe('search method integration', () => {
    it('should handle Arabic text search queries', async () => {
      const query: SearchQuery = {
        text: 'تطوير البرمجيات',
        filters: {
          contentType: ['problem'],
          sectors: ['technology']
        },
        sortBy: 'relevance',
        pagination: { limit: 10, offset: 0 },
        language: 'ar'
      }

      // Mock successful database response
      const mockResults = [
        {
          id: '1',
          type: 'problem',
          title: 'مشكلة في تطوير البرمجيات',
          description: 'وصف المشكلة',
          category: 'تطوير',
          sector: 'technology',
          tags: ['برمجة', 'تطوير'],
          relevance_score: 0.9,
          metadata: {
            status: 'open',
            created_at: '2024-01-01T00:00:00Z'
          }
        }
      ]

      // Mock the database call
      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockResults,
        error: null
      })

      const results = await searchService.search(query)

      expect(results.items).toHaveLength(1)
      expect(results.items[0].title).toBe('مشكلة في تطوير البرمجيات')
      expect(results.items[0].type).toBe('problem')
    })

    it('should handle English text search queries', async () => {
      const query: SearchQuery = {
        text: 'software development',
        filters: {
          contentType: ['expert'],
          availability: ['available']
        },
        sortBy: 'rating',
        pagination: { limit: 5, offset: 0 },
        language: 'en'
      }

      const mockResults = [
        {
          id: '2',
          type: 'expert',
          title: 'Software Development Expert',
          description: 'Experienced developer',
          category: 'software',
          sector: 'technology',
          tags: ['javascript', 'react', 'node'],
          relevance_score: 0.85,
          metadata: {
            rating: 4.5,
            availability: 'available',
            created_at: '2024-01-02T00:00:00Z'
          }
        }
      ]

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockResults,
        error: null
      })

      const results = await searchService.search(query)

      expect(results.items).toHaveLength(1)
      expect(results.items[0].title).toBe('Software Development Expert')
      expect(results.items[0].type).toBe('expert')
    })

    it('should handle database errors gracefully', async () => {
      const query: SearchQuery = {
        text: 'test query',
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 10, offset: 0 }
      }

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: null,
        error: { message: 'Database connection failed' }
      })

      await expect(searchService.search(query)).rejects.toThrow('Database connection failed')
    })

    it('should handle empty search results', async () => {
      const query: SearchQuery = {
        text: 'nonexistent query',
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 10, offset: 0 }
      }

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: [],
        error: null
      })

      const results = await searchService.search(query)

      expect(results.items).toHaveLength(0)
      expect(results.totalCount).toBe(0)
      expect(results.hasMore).toBe(false)
    })
  })

  describe('complex filtering scenarios', () => {
    it('should handle multiple content types', async () => {
      const query: SearchQuery = {
        text: 'database',
        filters: {
          contentType: ['problem', 'solution'],
          sectors: ['technology', 'finance']
        },
        sortBy: 'date',
        pagination: { limit: 20, offset: 0 }
      }

      const mockResults = [
        {
          id: '1',
          type: 'problem',
          title: 'Database Performance Issue',
          description: 'Slow queries',
          category: 'database',
          sector: 'technology',
          tags: ['performance', 'sql'],
          relevance_score: 0.8,
          metadata: { status: 'open', created_at: '2024-01-01T00:00:00Z' }
        },
        {
          id: '2',
          type: 'solution',
          title: 'Database Optimization Solution',
          description: 'Query optimization techniques',
          category: 'database',
          sector: 'finance',
          tags: ['optimization', 'indexing'],
          relevance_score: 0.75,
          metadata: { status: 'approved', created_at: '2024-01-02T00:00:00Z' }
        }
      ]

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockResults,
        error: null
      })

      const results = await searchService.search(query)

      expect(results.items).toHaveLength(2)
      expect(results.items.some(item => item.type === 'problem')).toBe(true)
      expect(results.items.some(item => item.type === 'solution')).toBe(true)
    })

    it('should handle date range filtering', async () => {
      const query: SearchQuery = {
        text: 'recent problems',
        filters: {
          contentType: ['problem'],
          dateRange: {
            start: '2024-01-01T00:00:00Z',
            end: '2024-12-31T23:59:59Z'
          }
        },
        sortBy: 'date',
        pagination: { limit: 10, offset: 0 }
      }

      const mockResults = [
        {
          id: '1',
          type: 'problem',
          title: 'Recent Problem',
          description: 'A recent issue',
          category: 'general',
          sector: 'technology',
          tags: ['recent'],
          relevance_score: 0.7,
          metadata: {
            status: 'open',
            created_at: '2024-06-15T10:00:00Z'
          }
        }
      ]

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockResults,
        error: null
      })

      const results = await searchService.search(query)

      expect(results.items).toHaveLength(1)
      expect(new Date(results.items[0].metadata.created_at).getFullYear()).toBe(2024)
    })

    it('should handle rating range filtering', async () => {
      const query: SearchQuery = {
        text: 'high rated experts',
        filters: {
          contentType: ['expert'],
          rating: { min: 4.0, max: 5.0 }
        },
        sortBy: 'rating',
        pagination: { limit: 10, offset: 0 }
      }

      const mockResults = [
        {
          id: '1',
          type: 'expert',
          title: 'Top Expert',
          description: 'Highly rated expert',
          category: 'software',
          sector: 'technology',
          tags: ['expert', 'experienced'],
          relevance_score: 0.9,
          metadata: {
            rating: 4.8,
            availability: 'available',
            created_at: '2024-01-01T00:00:00Z'
          }
        }
      ]

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockResults,
        error: null
      })

      const results = await searchService.search(query)

      expect(results.items).toHaveLength(1)
      expect(results.items[0].metadata.rating).toBeGreaterThanOrEqual(4.0)
      expect(results.items[0].metadata.rating).toBeLessThanOrEqual(5.0)
    })
  })

  describe('pagination and sorting', () => {
    it('should handle pagination correctly', async () => {
      const query: SearchQuery = {
        text: 'test',
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 5, offset: 10 }
      }

      const mockResults = Array.from({ length: 5 }, (_, i) => ({
        id: `${i + 11}`,
        type: 'problem' as const,
        title: `Problem ${i + 11}`,
        description: `Description ${i + 11}`,
        category: 'test',
        sector: 'technology',
        tags: ['test'],
        relevance_score: 0.5,
        metadata: { status: 'open', created_at: '2024-01-01T00:00:00Z' }
      }))

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockResults,
        error: null
      })

      const results = await searchService.search(query)

      expect(results.items).toHaveLength(5)
      expect(results.items[0].id).toBe('11') // Should start from offset + 1
    })

    it('should handle different sort options', async () => {
      const baseQuery = {
        text: 'test',
        filters: {},
        pagination: { limit: 10, offset: 0 }
      }

      const mockResults = [
        {
          id: '1',
          type: 'problem' as const,
          title: 'Problem 1',
          description: 'Description 1',
          category: 'test',
          sector: 'technology',
          tags: ['test'],
          relevance_score: 0.8,
          metadata: {
            status: 'open',
            created_at: '2024-01-01T00:00:00Z',
            rating: 3.5
          }
        }
      ]

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockResults,
        error: null
      })

      // Test different sort options
      const sortOptions = ['relevance', 'date', 'rating', 'popularity'] as const
      
      for (const sortBy of sortOptions) {
        const query: SearchQuery = { ...baseQuery, sortBy }
        const results = await searchService.search(query)
        
        expect(results.items).toHaveLength(1)
        expect(supabase.rpc).toHaveBeenCalled()
      }
    })
  })

  describe('fuzzy matching and similarity', () => {
    it('should handle typos and similar terms', async () => {
      const query: SearchQuery = {
        text: 'databse', // Intentional typo
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 10, offset: 0 }
      }

      const mockResults = [
        {
          id: '1',
          type: 'problem',
          title: 'Database Connection Issue',
          description: 'Cannot connect to database',
          category: 'database',
          sector: 'technology',
          tags: ['database', 'connection'],
          relevance_score: 0.7, // Lower score due to fuzzy match
          metadata: { status: 'open', created_at: '2024-01-01T00:00:00Z' }
        }
      ]

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockResults,
        error: null
      })

      const results = await searchService.search(query)

      expect(results.items).toHaveLength(1)
      expect(results.items[0].title).toContain('Database')
      expect(results.items[0].relevanceScore).toBeLessThan(0.8) // Should have lower relevance due to fuzzy match
    })
  })

  describe('performance and caching', () => {
    it('should handle large result sets efficiently', async () => {
      const query: SearchQuery = {
        text: 'common term',
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 100, offset: 0 }
      }

      // Generate large mock result set
      const mockResults = Array.from({ length: 100 }, (_, i) => ({
        id: `${i + 1}`,
        type: 'problem' as const,
        title: `Problem ${i + 1}`,
        description: `Description ${i + 1}`,
        category: 'test',
        sector: 'technology',
        tags: ['common'],
        relevance_score: Math.random(),
        metadata: { status: 'open', created_at: '2024-01-01T00:00:00Z' }
      }))

      const { supabase } = await import('@/lib/database')
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: mockResults,
        error: null
      })

      const startTime = Date.now()
      const results = await searchService.search(query)
      const endTime = Date.now()

      expect(results.items).toHaveLength(100)
      expect(endTime - startTime).toBeLessThan(1000) // Should complete within 1 second
    })
  })
})