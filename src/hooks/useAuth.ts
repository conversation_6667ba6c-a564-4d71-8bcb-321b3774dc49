import { useState, useEffect } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

interface AuthState {
  user: User | null
  session: Session | null
  loading: boolean
}

interface AuthActions {
  signUp: (email: string, password: string, userData?: any) => Promise<{ error: AuthError | null }>
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
  updateProfile: (updates: any) => Promise<{ error: AuthError | null }>
}

export function useAuth(): AuthState & AuthActions {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)

      // Update last_login_at when user signs in
      if (event === 'SIGNED_IN' && session?.user) {
        await supabase
          .from('users')
          .update({ last_login_at: new Date().toISOString() })
          .eq('id', session.user.id)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const signUp = async (email: string, password: string, userData?: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: userData?.name,
          role: userData?.role || 'ministry_user',
          location: userData?.location || 'Damascus, Syria',
          organization: userData?.organization,
          position: userData?.position,
          phone_number: userData?.phone_number,
          bio: userData?.bio
        }
      }
    })

    // If signup successful and user is created, create user profile
    if (!error && data.user) {
      try {
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email: data.user.email!,
            name: userData?.name || data.user.email!,
            role: userData?.role || 'ministry_user',
            location: userData?.location || 'Damascus, Syria',
            organization: userData?.organization || null,
            position: userData?.position || null,
            phone_number: userData?.phone_number || null,
            bio: userData?.bio || null,
            languages: ['ar']
          })

        if (profileError) {
          console.error('Error creating user profile:', profileError)
        }

        // If user is signing up as expert, create expert profile
        if (userData?.role === 'expert') {
          const { error: expertError } = await supabase
            .from('experts')
            .insert({
              user_id: data.user.id,
              expertise_areas: [],
              experience_years: 0,
              availability: 'available',
              rating: 0,
              total_contributions: 0,
              success_rate: 0,
              response_time_hours: 24,
              portfolio: [],
              certifications: []
            })

          if (expertError) {
            console.error('Error creating expert profile:', expertError)
          }
        }
      } catch (profileCreationError) {
        console.error('Error in profile creation:', profileCreationError)
      }
    }

    return { error }
  }

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { error }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  }

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    })
    return { error }
  }

  const updateProfile = async (updates: any) => {
    const { error } = await supabase.auth.updateUser({
      data: updates
    })
    return { error }
  }

  return {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updateProfile
  }
}