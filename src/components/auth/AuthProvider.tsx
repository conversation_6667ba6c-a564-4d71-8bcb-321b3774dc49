import React, { create<PERSON>ontext, use<PERSON>ontext, ReactN<PERSON>, useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Lock, AlertTriangle } from 'lucide-react'
import { Link } from 'react-router-dom'

interface UserData {
  id: string
  email: string
  name: string
  role: 'expert' | 'ministry_user' | 'admin'
  avatar?: string
  bio?: string
  location: string
  phone_number?: string
  organization?: string
  position?: string
  languages: string[]
  is_active: boolean
  created_at: string
  last_login_at?: string
}

interface AuthContextType {
  user: User | null
  userData: UserData | null
  session: Session | null
  loading: boolean
  signUp: (email: string, password: string, userData?: any) => Promise<{ error: any }>
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signOut: () => Promise<{ error: any }>
  resetPassword: (email: string) => Promise<{ error: any }>
  updateProfile: (updates: any) => Promise<{ error: any }>
  hasRole: (role: 'expert' | 'ministry_user' | 'admin') => boolean
  hasAnyRole: (roles: ('expert' | 'ministry_user' | 'admin')[]) => boolean
  isAdmin: boolean
  isExpert: boolean
  isMinistryUser: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const auth = useAuth()
  const [userData, setUserData] = useState<UserData | null>(null)
  const [userDataLoading, setUserDataLoading] = useState(false)

  // Fetch user data when user changes
  useEffect(() => {
    if (auth.user && !userData) {
      fetchUserData()
    } else if (!auth.user) {
      setUserData(null)
    }
  }, [auth.user])

  const fetchUserData = async () => {
    if (!auth.user) return

    setUserDataLoading(true)
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', auth.user.id)
        .single()

      if (error) {
        console.error('Error fetching user data:', error)
        return
      }

      setUserData(data)
    } catch (err) {
      console.error('Unexpected error fetching user data:', err)
    } finally {
      setUserDataLoading(false)
    }
  }

  const hasRole = (role: 'expert' | 'ministry_user' | 'admin'): boolean => {
    return userData?.role === role
  }

  const hasAnyRole = (roles: ('expert' | 'ministry_user' | 'admin')[]): boolean => {
    return userData ? roles.includes(userData.role) : false
  }

  const isAdmin = userData?.role === 'admin'
  const isExpert = userData?.role === 'expert'
  const isMinistryUser = userData?.role === 'ministry_user'

  const contextValue: AuthContextType = {
    ...auth,
    userData,
    hasRole,
    hasAnyRole,
    isAdmin,
    isExpert,
    isMinistryUser,
    loading: auth.loading || userDataLoading
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuthContext() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider')
  }
  return context
}

// Loading component
function LoadingScreen() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-600" />
        <h2 className="text-xl font-semibold mb-2">جاري التحميل...</h2>
        <p className="text-gray-600">يرجى الانتظار</p>
      </div>
    </div>
  )
}

// Authentication required component
function AuthRequired() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Lock className="h-12 w-12 mx-auto mb-4 text-blue-600" />
          <CardTitle className="text-2xl">تسجيل الدخول مطلوب</CardTitle>
          <CardDescription>
            يجب تسجيل الدخول للوصول إلى هذه الصفحة
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button asChild className="w-full">
            <Link to="/auth/login">تسجيل الدخول</Link>
          </Button>
          <Button asChild variant="outline" className="w-full">
            <Link to="/auth/register">إنشاء حساب جديد</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

// Access denied component
function AccessDenied({ requiredRole }: { requiredRole?: string }) {
  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin': return 'مدير'
      case 'expert': return 'خبير'
      case 'ministry_user': return 'موظف وزارة'
      default: return role
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-600" />
          <CardTitle className="text-2xl">غير مصرح بالوصول</CardTitle>
          <CardDescription>
            {requiredRole 
              ? `هذه الصفحة متاحة فقط للمستخدمين من نوع: ${getRoleLabel(requiredRole)}`
              : 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button asChild variant="outline" className="w-full">
            <Link to="/">العودة إلى الصفحة الرئيسية</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

// Account inactive component
function AccountInactive() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-yellow-600" />
          <CardTitle className="text-2xl">الحساب غير نشط</CardTitle>
          <CardDescription>
            تم إلغاء تفعيل حسابك. يرجى التواصل مع الإدارة
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertDescription>
              إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع فريق الدعم
            </AlertDescription>
          </Alert>
          <Button asChild variant="outline" className="w-full">
            <Link to="/contact">التواصل مع الدعم</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

// Protected route component with enhanced role-based access control
export function ProtectedRoute({ 
  children, 
  requiredRole,
  requiredRoles,
  requireAuth = true
}: { 
  children: ReactNode
  requiredRole?: 'expert' | 'ministry_user' | 'admin'
  requiredRoles?: ('expert' | 'ministry_user' | 'admin')[]
  requireAuth?: boolean
}) {
  const { user, userData, loading, hasRole, hasAnyRole } = useAuthContext()

  // Show loading screen while authentication is being checked
  if (loading) {
    return <LoadingScreen />
  }

  // Check if authentication is required
  if (requireAuth && !user) {
    return <AuthRequired />
  }

  // If user is authenticated but no userData, show loading
  if (user && !userData && requireAuth) {
    return <LoadingScreen />
  }

  // Check if account is active
  if (userData && !userData.is_active) {
    return <AccountInactive />
  }

  // Check role-based access control
  if (requiredRole && !hasRole(requiredRole)) {
    return <AccessDenied requiredRole={requiredRole} />
  }

  if (requiredRoles && !hasAnyRole(requiredRoles)) {
    return <AccessDenied />
  }

  return <>{children}</>
}

// Hook for role-based conditional rendering
export function useRoleAccess() {
  const { hasRole, hasAnyRole, isAdmin, isExpert, isMinistryUser } = useAuthContext()
  
  return {
    hasRole,
    hasAnyRole,
    isAdmin,
    isExpert,
    isMinistryUser,
    canCreateProblems: hasAnyRole(['ministry_user', 'admin']),
    canCreateSolutions: hasAnyRole(['expert', 'admin']),
    canModerateContent: isAdmin,
    canManageUsers: isAdmin,
    canViewAnalytics: hasAnyRole(['admin', 'ministry_user'])
  }
}