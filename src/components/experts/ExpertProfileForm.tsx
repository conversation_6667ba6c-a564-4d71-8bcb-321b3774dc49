import { useState, useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Plus, X, Save, User, Award, MapPin, Clock, Star } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { expertOperations } from '@/lib/database'
import { useAuth } from '@/hooks/useAuth'

const expertiseCategories = [
  'تطوير البرمجيات',
  'أمن المعلومات',
  'الذكاء الاصطناعي',
  'علوم البيانات',
  'الحوسبة السحابية',
  'إنترنت الأشياء',
  'تطوير المواقع',
  'تطوير التطبيقات',
  'إدارة قواعد البيانات',
  'الشبكات والاتصالات',
  'أنظمة التشغيل',
  'DevOps',
  'البلوك تشين',
  'الواقع المعزز والافتراضي',
  'التجارة الإلكترونية'
]

const skillLevels = [
  { value: 'beginner', label: 'مبتدئ' },
  { value: 'intermediate', label: 'متوسط' },
  { value: 'advanced', label: 'متقدم' },
  { value: 'expert', label: 'خبير' }
]

const availabilityOptions = [
  { value: 'available', label: 'متاح' },
  { value: 'busy', label: 'مشغول' },
  { value: 'unavailable', label: 'غير متاح' }
]

const expertProfileSchema = z.object({
  expertise_areas: z.array(z.object({
    category: z.string().min(1, 'يجب اختيار فئة الخبرة'),
    skills: z.array(z.string()).min(1, 'يجب إضافة مهارة واحدة على الأقل'),
    proficiencyLevel: z.enum(['beginner', 'intermediate', 'advanced', 'expert']),
    yearsOfExperience: z.number().min(0, 'سنوات الخبرة يجب أن تكون رقم موجب')
  })).min(1, 'يجب إضافة مجال خبرة واحد على الأقل'),
  experience_years: z.number().min(0, 'سنوات الخبرة الإجمالية يجب أن تكون رقم موجب'),
  availability: z.enum(['available', 'busy', 'unavailable']),
  response_time_hours: z.number().min(1, 'وقت الاستجابة يجب أن يكون ساعة واحدة على الأقل').max(168, 'وقت الاستجابة لا يمكن أن يتجاوز أسبوع'),
  portfolio: z.array(z.object({
    title: z.string().min(1, 'عنوان المشروع مطلوب'),
    description: z.string().min(10, 'وصف المشروع يجب أن يكون 10 أحرف على الأقل'),
    technologies: z.array(z.string()).min(1, 'يجب إضافة تقنية واحدة على الأقل'),
    url: z.string().url('رابط غير صحيح').optional().or(z.literal('')),
    completedAt: z.string().min(1, 'تاريخ الإنجاز مطلوب')
  })).optional(),
  certifications: z.array(z.object({
    name: z.string().min(1, 'اسم الشهادة مطلوب'),
    issuer: z.string().min(1, 'جهة الإصدار مطلوبة'),
    issuedAt: z.string().min(1, 'تاريخ الإصدار مطلوب'),
    expiresAt: z.string().optional(),
    credentialId: z.string().optional(),
    url: z.string().url('رابط غير صحيح').optional().or(z.literal(''))
  })).optional()
})

type ExpertProfileFormData = z.infer<typeof expertProfileSchema>

interface ExpertProfileFormProps {
  initialData?: any
  onSuccess?: () => void
}

export function ExpertProfileForm({ initialData, onSuccess }: ExpertProfileFormProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [newSkill, setNewSkill] = useState('')

  const form = useForm<ExpertProfileFormData>({
    resolver: zodResolver(expertProfileSchema),
    defaultValues: {
      expertise_areas: initialData?.expertise_areas || [
        {
          category: '',
          skills: [],
          proficiencyLevel: 'intermediate',
          yearsOfExperience: 0
        }
      ],
      experience_years: initialData?.experience_years || 0,
      availability: initialData?.availability || 'available',
      response_time_hours: initialData?.response_time_hours || 24,
      portfolio: initialData?.portfolio || [],
      certifications: initialData?.certifications || []
    }
  })

  const { fields: expertiseFields, append: appendExpertise, remove: removeExpertise } = useFieldArray({
    control: form.control,
    name: 'expertise_areas'
  })

  const { fields: portfolioFields, append: appendPortfolio, remove: removePortfolio } = useFieldArray({
    control: form.control,
    name: 'portfolio'
  })

  const { fields: certificationFields, append: appendCertification, remove: removeCertification } = useFieldArray({
    control: form.control,
    name: 'certifications'
  })

  const addSkillToExpertise = (expertiseIndex: number, skill: string) => {
    if (!skill.trim()) return
    
    const currentSkills = form.getValues(`expertise_areas.${expertiseIndex}.skills`) || []
    if (!currentSkills.includes(skill.trim())) {
      form.setValue(`expertise_areas.${expertiseIndex}.skills`, [...currentSkills, skill.trim()])
    }
    setNewSkill('')
  }

  const removeSkillFromExpertise = (expertiseIndex: number, skillIndex: number) => {
    const currentSkills = form.getValues(`expertise_areas.${expertiseIndex}.skills`) || []
    const updatedSkills = currentSkills.filter((_, index) => index !== skillIndex)
    form.setValue(`expertise_areas.${expertiseIndex}.skills`, updatedSkills)
  }

  const onSubmit = async (data: ExpertProfileFormData) => {
    if (!user) {
      toast({
        title: 'خطأ',
        description: 'يجب تسجيل الدخول أولاً',
        variant: 'destructive'
      })
      return
    }

    setIsSubmitting(true)
    try {
      let result
      if (initialData) {
        // Update existing profile
        result = await expertOperations.updateExpertProfile(user.id, data)
      } else {
        // Create new profile
        result = await expertOperations.createExpertProfile({
          user_id: user.id,
          ...data
        })
      }

      if (result.error) {
        throw new Error(result.error.message)
      }

      toast({
        title: 'تم الحفظ بنجاح',
        description: initialData ? 'تم تحديث ملفك الشخصي' : 'تم إنشاء ملفك الشخصي كخبير'
      })

      onSuccess?.()
    } catch (error) {
      console.error('Error saving expert profile:', error)
      toast({
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ أثناء حفظ البيانات. يرجى المحاولة مرة أخرى.',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            {initialData ? 'تحديث الملف الشخصي للخبير' : 'إنشاء ملف شخصي للخبير'}
          </CardTitle>
          <CardDescription>
            املأ المعلومات التالية لإنشاء أو تحديث ملفك الشخصي كخبير تقني
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="experience_years"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        سنوات الخبرة الإجمالية
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="availability"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>حالة التوفر</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر حالة التوفر" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availabilityOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="response_time_hours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>وقت الاستجابة (بالساعات)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          max="168"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 24)}
                        />
                      </FormControl>
                      <FormDescription>
                        الوقت المتوقع للرد على الاستفسارات (1-168 ساعة)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              {/* Expertise Areas */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    مجالات الخبرة
                  </h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => appendExpertise({
                      category: '',
                      skills: [],
                      proficiencyLevel: 'intermediate',
                      yearsOfExperience: 0
                    })}
                  >
                    <Plus className="w-4 h-4 ml-2" />
                    إضافة مجال خبرة
                  </Button>
                </div>

                {expertiseFields.map((field, index) => (
                  <Card key={field.id} className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <h4 className="font-medium">مجال الخبرة {index + 1}</h4>
                      {expertiseFields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeExpertise(index)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <FormField
                        control={form.control}
                        name={`expertise_areas.${index}.category`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>فئة الخبرة</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="اختر فئة الخبرة" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {expertiseCategories.map((category) => (
                                  <SelectItem key={category} value={category}>
                                    {category}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`expertise_areas.${index}.proficiencyLevel`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>مستوى الإتقان</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="اختر مستوى الإتقان" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {skillLevels.map((level) => (
                                  <SelectItem key={level.value} value={level.value}>
                                    {level.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`expertise_areas.${index}.yearsOfExperience`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>سنوات الخبرة في هذا المجال</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Skills Management */}
                    <div className="space-y-2">
                      <FormLabel>المهارات التقنية</FormLabel>
                      <div className="flex gap-2">
                        <Input
                          placeholder="أضف مهارة جديدة..."
                          value={newSkill}
                          onChange={(e) => setNewSkill(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault()
                              addSkillToExpertise(index, newSkill)
                            }
                          }}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => addSkillToExpertise(index, newSkill)}
                        >
                          إضافة
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {form.watch(`expertise_areas.${index}.skills`)?.map((skill, skillIndex) => (
                          <Badge key={skillIndex} variant="secondary" className="flex items-center gap-1">
                            {skill}
                            <X
                              className="w-3 h-3 cursor-pointer"
                              onClick={() => removeSkillFromExpertise(index, skillIndex)}
                            />
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              <Separator />

              {/* Portfolio Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">معرض الأعمال</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => appendPortfolio({
                      title: '',
                      description: '',
                      technologies: [],
                      url: '',
                      completedAt: ''
                    })}
                  >
                    <Plus className="w-4 h-4 ml-2" />
                    إضافة مشروع
                  </Button>
                </div>

                {portfolioFields.map((field, index) => (
                  <Card key={field.id} className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <h4 className="font-medium">مشروع {index + 1}</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removePortfolio(index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`portfolio.${index}.title`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>عنوان المشروع</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`portfolio.${index}.completedAt`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تاريخ الإنجاز</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`portfolio.${index}.url`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>رابط المشروع (اختياري)</FormLabel>
                            <FormControl>
                              <Input type="url" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`portfolio.${index}.description`}
                      render={({ field }) => (
                        <FormItem className="mt-4">
                          <FormLabel>وصف المشروع</FormLabel>
                          <FormControl>
                            <Textarea {...field} rows={3} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </Card>
                ))}
              </div>

              <Separator />

              {/* Certifications Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Star className="w-5 h-5" />
                    الشهادات والمؤهلات
                  </h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => appendCertification({
                      name: '',
                      issuer: '',
                      issuedAt: '',
                      expiresAt: '',
                      credentialId: '',
                      url: ''
                    })}
                  >
                    <Plus className="w-4 h-4 ml-2" />
                    إضافة شهادة
                  </Button>
                </div>

                {certificationFields.map((field, index) => (
                  <Card key={field.id} className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <h4 className="font-medium">شهادة {index + 1}</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCertification(index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`certifications.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>اسم الشهادة</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`certifications.${index}.issuer`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>جهة الإصدار</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`certifications.${index}.issuedAt`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تاريخ الإصدار</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`certifications.${index}.expiresAt`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>تاريخ الانتهاء (اختياري)</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`certifications.${index}.credentialId`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>رقم الشهادة (اختياري)</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`certifications.${index}.url`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>رابط التحقق (اختياري)</FormLabel>
                            <FormControl>
                              <Input type="url" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </Card>
                ))}
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button type="submit" disabled={isSubmitting} className="min-w-32">
                  {isSubmitting ? (
                    'جاري الحفظ...'
                  ) : (
                    <>
                      <Save className="w-4 h-4 ml-2" />
                      {initialData ? 'تحديث الملف الشخصي' : 'إنشاء الملف الشخصي'}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}