import { memo, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDeviceType } from '@/hooks/use-mobile';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { ProblemCard } from './ProblemCard';
import { Problem } from '@/types';
import { 
  Plus, 
  AlertCircle,
  Loader2
} from 'lucide-react';

interface ProblemListProps {
  problems: Problem[];
  loading: boolean;
  hasFilters: boolean;
}

interface EmptyStateProps {
  message: string;
}

const EmptyState = memo<EmptyStateProps>(({ message }) => {
  const { t, isRTL } = useLanguage();
  const { user } = useAuthContext();

  return (
    <div className="text-center py-12">
      <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">{t('problems.no_problems')}</h3>
      <p className="text-gray-600 mb-6">{message}</p>
      {user && (
        <Button asChild>
          <Link to="/problems/new">
            <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('problems.add_new_problem')}
          </Link>
        </Button>
      )}
    </div>
  );
});

EmptyState.displayName = 'EmptyState';

const LoadingState = memo(() => (
  <div className="flex justify-center py-12">
    <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
  </div>
));

LoadingState.displayName = 'LoadingState';

export const ProblemList = memo<ProblemListProps>(({ problems, loading, hasFilters }) => {
  const { t } = useLanguage();
  const { isMobile } = useDeviceType();

  const emptyMessage = useMemo(() => {
    return hasFilters ? t('problems.no_results') : t('problems.no_problems_yet');
  }, [hasFilters, t]);

  const gridClassName = useMemo(() => {
    return `grid grid-cols-1 ${isMobile ? 'gap-4' : 'lg:grid-cols-2 gap-6'}`;
  }, [isMobile]);

  if (loading) {
    return <LoadingState />;
  }

  if (problems.length === 0) {
    return <EmptyState message={emptyMessage} />;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <p className="text-gray-600">
          {`${problems.length} ${t('problems.title').toLowerCase()}`}
        </p>
      </div>

      <div className={gridClassName}>
        {problems.map(problem => (
          <ProblemCard key={problem.id} problem={problem} />
        ))}
      </div>
    </div>
  );
});

ProblemList.displayName = 'ProblemList';