import { memo, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDeviceType } from '@/hooks/use-mobile';
import { Problem } from '@/types';
import { 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Users,
  Calendar,
  Tag,
  FileText
} from 'lucide-react';

interface ProblemCardProps {
  problem: Problem;
}

const getUrgencyConfig = (t: (key: string) => string) => ({
  low: { label: t('priority.low'), color: 'bg-green-100 text-green-800', icon: '🟢' },
  medium: { label: t('priority.medium'), color: 'bg-yellow-100 text-yellow-800', icon: '🟡' },
  high: { label: t('priority.high'), color: 'bg-orange-100 text-orange-800', icon: '🟠' },
  critical: { label: t('priority.critical'), color: 'bg-red-100 text-red-800', icon: '🔴' }
});

const getStatusConfig = (t: (key: string) => string) => ({
  open: { label: t('status.open'), color: 'bg-blue-100 text-blue-800', icon: <AlertCircle className="w-3 h-3" /> },
  in_progress: { label: t('status.in_progress'), color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="w-3 h-3" /> },
  resolved: { label: t('status.resolved'), color: 'bg-green-100 text-green-800', icon: <CheckCircle className="w-3 h-3" /> },
  closed: { label: t('status.closed'), color: 'bg-gray-100 text-gray-800', icon: <CheckCircle className="w-3 h-3" /> }
});

export const ProblemCard = memo<ProblemCardProps>(({ problem }) => {
  const { t, isRTL } = useLanguage();
  const { isMobile } = useDeviceType();

  const URGENCY_CONFIG = getUrgencyConfig(t);
  const STATUS_CONFIG = getStatusConfig(t);

  const formatDate = useCallback((dateString: string) => {
    const locale = isRTL ? 'ar-SA' : 'en-US';
    return new Date(dateString).toLocaleDateString(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }, [isRTL]);

  const formatTimeAgo = useCallback((dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return t('time.hour_ago');
    if (diffInHours < 24) return `${diffInHours} ${t('time.hours_ago')}`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} ${t('time.days_ago')}`;
    
    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) return `${diffInWeeks} ${t('time.weeks_ago')}`;
    
    return formatDate(dateString);
  }, [formatDate, t]);

  const handleCardClick = useCallback((e: React.MouseEvent) => {
    // Prevent navigation if clicking on interactive elements
    if ((e.target as HTMLElement).closest('a, button')) {
      return;
    }
  }, []);

  return (
    <Card 
      className="hover:shadow-md transition-shadow touch-manipulation cursor-pointer"
      onClick={handleCardClick}
    >
      <CardHeader className={`${isMobile ? 'pb-2 p-4' : 'pb-3'}`}>
        <div className={`${isMobile ? 'flex flex-col gap-3' : 'flex justify-between items-start gap-4'}`}>
          <div className="flex-1 min-w-0">
            <CardTitle className={`leading-tight mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>
              <Link 
                to={`/problems/${problem.id}`}
                className="hover:text-blue-600 transition-colors touch-manipulation"
              >
                {problem.title}
              </Link>
            </CardTitle>
            <CardDescription className={`line-clamp-2 ${isMobile ? 'text-xs' : ''}`}>
              {problem.description}
            </CardDescription>
          </div>
          
          <div className={`flex gap-2 ${isMobile ? 'flex-row justify-start' : 'flex-col items-end'}`}>
            <Badge className={`${STATUS_CONFIG[problem.status].color} ${isMobile ? 'text-xs px-2 py-1' : ''}`}>
              {STATUS_CONFIG[problem.status].icon}
              {STATUS_CONFIG[problem.status].label}
            </Badge>
            <Badge variant="outline" className={`${URGENCY_CONFIG[problem.urgency].color} ${isMobile ? 'text-xs px-2 py-1' : ''}`}>
              {URGENCY_CONFIG[problem.urgency].icon} {URGENCY_CONFIG[problem.urgency].label}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className={`pt-0 ${isMobile ? 'p-4 pt-0' : ''}`}>
        <div className={`${isMobile ? 'space-y-2' : 'space-y-3'}`}>
          {/* Meta Information */}
          <div className={`flex flex-wrap gap-2 text-gray-600 ${isMobile ? 'text-xs' : 'text-sm gap-4'}`}>
            <div className="flex items-center gap-1">
              <Tag className="w-3 h-3" />
              <span className="truncate max-w-24">{problem.category}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="w-3 h-3" />
              <span className="truncate max-w-24">{problem.sector}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              {formatTimeAgo(problem.created_at)}
            </div>
            {problem.attachments?.length > 0 && (
              <div className="flex items-center gap-1">
                <FileText className="w-3 h-3" />
                {problem.attachments.length} {problem.attachments.length === 1 ? t('problems.attachment') : t('problems.attachments')}
              </div>
            )}
          </div>

          {/* Tags */}
          {problem.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {problem.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {problem.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{problem.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* Submitter Info */}
          <div className="flex justify-between items-center text-sm">
            <div className="text-gray-600">
              {t('problems.by')}: {problem.users?.name || 'User'}
              {problem.users?.organization && (
                <span className="text-gray-500"> - {problem.users.organization}</span>
              )}
            </div>
            <div className="text-gray-500">
              {problem.solutions?.length || 0} {t('problems.solutions_count')}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

ProblemCard.displayName = 'ProblemCard';