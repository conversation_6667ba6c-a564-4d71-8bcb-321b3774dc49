import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDeviceType } from '@/hooks/use-mobile';
import { GlobalSearch } from '@/components/search/GlobalSearch';
import { searchService } from '@/lib/search';
import { SearchFilters as SearchFiltersType } from '@/lib/search';
import { 
  Search, 
  SortAsc, 
  SortDesc, 
  Plus, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Users,
  Calendar,
  Tag,
  FileText,
  Loader2,
  X,
  Filter
} from 'lucide-react';

interface Problem {
  id: string;
  title: string;
  description: string;
  category: string;
  sector: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  submitted_by: string;
  tags: string[];
  attachments: any[];
  created_at: string;
  updated_at: string;
  users?: {
    name: string;
    organization?: string;
  };
  solutions?: any[];
}

interface Filters {
  search: string;
  status: string;
  urgency: string;
  category: string;
  sector: string;
  sortBy: 'created_at' | 'updated_at' | 'title';
  sortOrder: 'asc' | 'desc';
}

const getUrgencyConfig = (t: (key: string) => string) => ({
  low: { label: t('priority.low'), color: 'bg-green-100 text-green-800', icon: '🟢' },
  medium: { label: t('priority.medium'), color: 'bg-yellow-100 text-yellow-800', icon: '🟡' },
  high: { label: t('priority.high'), color: 'bg-orange-100 text-orange-800', icon: '🟠' },
  critical: { label: t('priority.critical'), color: 'bg-red-100 text-red-800', icon: '🔴' }
});

const getStatusConfig = (t: (key: string) => string) => ({
  open: { label: t('status.open'), color: 'bg-blue-100 text-blue-800', icon: <AlertCircle className="w-3 h-3" /> },
  in_progress: { label: t('status.in_progress'), color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="w-3 h-3" /> },
  resolved: { label: t('status.resolved'), color: 'bg-green-100 text-green-800', icon: <CheckCircle className="w-3 h-3" /> },
  closed: { label: t('status.closed'), color: 'bg-gray-100 text-gray-800', icon: <CheckCircle className="w-3 h-3" /> }
});

export function ProblemDashboard() {
  const [problems, setProblems] = useState<Problem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<Filters>({
    search: '',
    status: '',
    urgency: '',
    category: '',
    sector: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  
  const { toast } = useToast();
  const { user } = useAuthContext();
  const { t, isRTL } = useLanguage();
  const { isMobile } = useDeviceType();

  const URGENCY_CONFIG = getUrgencyConfig(t);
  const STATUS_CONFIG = getStatusConfig(t);

  // Get unique values for filter options
  const categories = [...new Set(problems.map(p => p.category))].filter(Boolean);
  const sectors = [...new Set(problems.map(p => p.sector))].filter(Boolean);

  const fetchProblems = async () => {
    setLoading(true);
    
    try {
      // Try to use search service for enhanced filtering
      if (filters.search.trim()) {
        const searchFilters: SearchFiltersType = {
          contentType: ['problem'],
          ...(filters.category && { categories: [filters.category] }),
          ...(filters.sector && { sectors: [filters.sector] }),
          ...(filters.status && { status: [filters.status] }),
          ...(filters.urgency && { urgency: [filters.urgency] })
        };

        const searchResults = await searchService.search({
          text: filters.search,
          filters: searchFilters,
          sortBy: filters.sortBy === 'created_at' ? 'date' : 
                  filters.sortBy === 'updated_at' ? 'date' : 'relevance',
          pagination: { limit: 50, offset: 0 },
          language: 'auto'
        });

        // Convert search results to Problem format
        const searchProblems = searchResults.items
          .filter(item => item.type === 'problem')
          .map(item => ({
            id: item.id,
            title: item.title,
            description: item.description,
            category: item.metadata?.category || 'غير محدد',
            sector: item.metadata?.sector || 'غير محدد',
            urgency: item.metadata?.urgency || 'medium',
            status: item.metadata?.status || 'open',
            submitted_by: item.metadata?.submitted_by || 'user',
            tags: item.tags || [],
            attachments: item.metadata?.attachments || [],
            created_at: item.metadata?.created_at || new Date().toISOString(),
            updated_at: item.metadata?.updated_at || new Date().toISOString(),
            users: {
              name: item.metadata?.submitter_name || 'مستخدم',
              organization: item.metadata?.organization
            },
            solutions: item.metadata?.solutions || []
          }));

        if (searchProblems.length > 0) {
          setProblems(searchProblems);
          setLoading(false);
          return;
        }
      }
    } catch (error) {
      console.warn('Search service not available, falling back to mock data:', error);
    }
    
    // Fallback to mock data
    const mockProblems: Problem[] = [
      {
        id: '1',
        title: 'تطوير نظام إدارة المستشفيات الرقمي',
        description: 'نحتاج إلى نظام شامل لإدارة المستشفيات يتضمن إدارة المرضى، الحجوزات، والمخزون الطبي',
        category: 'تطوير البرمجيات',
        sector: 'وزارة الصحة',
        urgency: 'high',
        status: 'open',
        submitted_by: 'user1',
        tags: ['نظم المعلومات الطبية', 'إدارة المستشفيات', 'قواعد البيانات'],
        attachments: [],
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        users: {
          name: 'د. أحمد الخطيب',
          organization: 'وزارة الصحة'
        },
        solutions: []
      },
      {
        id: '2',
        title: 'أتمتة عمليات التخليص الجمركي',
        description: 'نواجه تحديات في أتمتة عمليات التخليص الجمركي وربطها بالأنظمة المصرفية',
        category: 'أتمتة العمليات',
        sector: 'وزارة المالية',
        urgency: 'medium',
        status: 'in_progress',
        submitted_by: 'user2',
        tags: ['أتمتة', 'جمارك', 'تكامل الأنظمة'],
        attachments: [{ name: 'requirements.pdf', size: '2MB' }],
        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        users: {
          name: 'م. فاطمة السيد',
          organization: 'وزارة المالية'
        },
        solutions: [{ id: '1' }, { id: '2' }]
      },
      {
        id: '3',
        title: 'منصة التعليم الإلكتروني التفاعلي',
        description: 'تطوير منصة تعليم إلكتروني تفاعلية تدعم الفصول الافتراضية والتقييم الإلكتروني',
        category: 'تطوير المنصات',
        sector: 'وزارة التربية',
        urgency: 'critical',
        status: 'open',
        submitted_by: 'user3',
        tags: ['تعليم إلكتروني', 'فصول افتراضية', 'تقييم'],
        attachments: [],
        created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        users: {
          name: 'م. محمد العلي',
          organization: 'وزارة التربية'
        },
        solutions: []
      },
      {
        id: '4',
        title: 'نظام إدارة الموارد البشرية',
        description: 'نحتاج إلى نظام متكامل لإدارة الموارد البشرية يشمل الرواتب والإجازات والتقييم',
        category: 'أنظمة إدارية',
        sector: 'وزارة الإدارة المحلية',
        urgency: 'low',
        status: 'resolved',
        submitted_by: 'user4',
        tags: ['موارد بشرية', 'رواتب', 'إدارة'],
        attachments: [],
        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        users: {
          name: 'د. سارة أحمد',
          organization: 'وزارة الإدارة المحلية'
        },
        solutions: [{ id: '1' }]
      }
    ];
    
    setProblems(mockProblems);
    setLoading(false);
  };

  useEffect(() => {
    fetchProblems();
  }, []);

  // Filter and sort problems
  const filteredProblems = problems
    .filter(problem => {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          problem.title.toLowerCase().includes(searchLower) ||
          problem.description.toLowerCase().includes(searchLower) ||
          problem.tags.some(tag => tag.toLowerCase().includes(searchLower))
        );
      }
      return true;
    })
    .sort((a, b) => {
      const aValue = a[filters.sortBy];
      const bValue = b[filters.sortBy];
      
      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  const formatDate = (dateString: string) => {
    const locale = isRTL ? 'ar-SA' : 'en-US';
    return new Date(dateString).toLocaleDateString(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return t('time.hour_ago');
    if (diffInHours < 24) return `${diffInHours} ${t('time.hours_ago')}`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} ${t('time.days_ago')}`;
    
    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) return `${diffInWeeks} ${t('time.weeks_ago')}`;
    
    return formatDate(dateString);
  };

  const ProblemCard = ({ problem }: { problem: Problem }) => (
    <Card className="hover:shadow-md transition-shadow touch-manipulation">
      <CardHeader className={`${isMobile ? 'pb-2 p-4' : 'pb-3'}`}>
        <div className={`${isMobile ? 'flex flex-col gap-3' : 'flex justify-between items-start gap-4'}`}>
          <div className="flex-1 min-w-0">
            <CardTitle className={`leading-tight mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>
              <Link 
                to={`/problems/${problem.id}`}
                className="hover:text-blue-600 transition-colors touch-manipulation"
              >
                {problem.title}
              </Link>
            </CardTitle>
            <CardDescription className={`line-clamp-2 ${isMobile ? 'text-xs' : ''}`}>
              {problem.description}
            </CardDescription>
          </div>
          
          <div className={`flex gap-2 ${isMobile ? 'flex-row justify-start' : 'flex-col items-end'}`}>
            <Badge className={`${STATUS_CONFIG[problem.status].color} ${isMobile ? 'text-xs px-2 py-1' : ''}`}>
              {STATUS_CONFIG[problem.status].icon}
              {STATUS_CONFIG[problem.status].label}
            </Badge>
            <Badge variant="outline" className={`${URGENCY_CONFIG[problem.urgency].color} ${isMobile ? 'text-xs px-2 py-1' : ''}`}>
              {URGENCY_CONFIG[problem.urgency].icon} {URGENCY_CONFIG[problem.urgency].label}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className={`pt-0 ${isMobile ? 'p-4 pt-0' : ''}`}>
        <div className={`${isMobile ? 'space-y-2' : 'space-y-3'}`}>
          {/* Meta Information */}
          <div className={`flex flex-wrap gap-2 text-gray-600 ${isMobile ? 'text-xs' : 'text-sm gap-4'}`}>
            <div className="flex items-center gap-1">
              <Tag className="w-3 h-3" />
              <span className="truncate max-w-24">{problem.category}</span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="w-3 h-3" />
              <span className="truncate max-w-24">{problem.sector}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              {formatTimeAgo(problem.created_at)}
            </div>
            {problem.attachments?.length > 0 && (
              <div className="flex items-center gap-1">
                <FileText className="w-3 h-3" />
                {problem.attachments.length} {problem.attachments.length === 1 ? t('problems.attachment') : t('problems.attachments')}
              </div>
            )}
          </div>

          {/* Tags */}
          {problem.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {problem.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {problem.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{problem.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* Submitter Info */}
          <div className="flex justify-between items-center text-sm">
            <div className="text-gray-600">
              {t('problems.by')}: {problem.users?.name || 'User'}
              {problem.users?.organization && (
                <span className="text-gray-500"> - {problem.users.organization}</span>
              )}
            </div>
            <div className="text-gray-500">
              {problem.solutions?.length || 0} {t('problems.solutions_count')}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const EmptyState = ({ message }: { message: string }) => (
    <div className="text-center py-12">
      <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">{t('problems.no_problems')}</h3>
      <p className="text-gray-600 mb-6">{message}</p>
      {user && (
        <Button asChild>
          <Link to="/problems/new">
            <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('problems.add_new_problem')}
          </Link>
        </Button>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t('problems.title')}</h1>
          <p className="text-gray-600 mt-1">
            {t('problems.subtitle')}
          </p>
        </div>
        {user && (
          <Button asChild>
            <Link to="/problems/new">
              <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('problems.add_problem')}
            </Link>
          </Button>
        )}
      </div>

      {/* Enhanced Search Integration */}
      <Card>
        <CardHeader className={isMobile ? 'p-4' : ''}>
          <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'} flex items-center gap-2`}>
            <Search className="w-5 h-5" />
            {t('problems.search_filter')}
          </CardTitle>
        </CardHeader>
        <CardContent className={`${isMobile ? 'space-y-3 p-4 pt-0' : 'space-y-4'}`}>
          {/* Enhanced Search Component */}
          <div className="mb-4">
            <GlobalSearch />
          </div>
          
          {/* Traditional Search Fallback */}
          <div className="relative">
            <Search className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4`} />
            <Input
              placeholder={t('problems.search_placeholder')}
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className={`${isRTL ? 'pr-10' : 'pl-10'} ${isMobile ? 'text-base' : ''} touch-manipulation`}
            />
          </div>

          {/* Filter Row */}
          <div className={`grid grid-cols-1 gap-3 ${isMobile ? '' : 'md:grid-cols-2 lg:grid-cols-5 gap-4'}`}>
            <Select 
              value={filters.status} 
              onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('problems.status')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">{t('problems.all_statuses')}</SelectItem>
                {Object.entries(STATUS_CONFIG).map(([key, config]) => (
                  <SelectItem key={key} value={key}>
                    <div className="flex items-center gap-2">
                      {config.icon}
                      {config.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select 
              value={filters.urgency} 
              onValueChange={(value) => setFilters(prev => ({ ...prev, urgency: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('problems.priority')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">{t('problems.all_priorities')}</SelectItem>
                {Object.entries(URGENCY_CONFIG).map(([key, config]) => (
                  <SelectItem key={key} value={key}>
                    <div className="flex items-center gap-2">
                      <span>{config.icon}</span>
                      {config.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select 
              value={filters.category} 
              onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('problems.category')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">{t('problems.all_categories')}</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select 
              value={filters.sector} 
              onValueChange={(value) => setFilters(prev => ({ ...prev, sector: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('problems.sector')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">{t('problems.all_sectors')}</SelectItem>
                {sectors.map(sector => (
                  <SelectItem key={sector} value={sector}>
                    {sector}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select 
              value={`${filters.sortBy}-${filters.sortOrder}`} 
              onValueChange={(value) => {
                const [sortBy, sortOrder] = value.split('-') as [typeof filters.sortBy, typeof filters.sortOrder];
                setFilters(prev => ({ ...prev, sortBy, sortOrder }));
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('problems.sort')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at-desc">
                  <div className="flex items-center gap-2">
                    <SortDesc className="w-4 h-4" />
                    {t('problems.newest_first')}
                  </div>
                </SelectItem>
                <SelectItem value="created_at-asc">
                  <div className="flex items-center gap-2">
                    <SortAsc className="w-4 h-4" />
                    {t('problems.oldest_first')}
                  </div>
                </SelectItem>
                <SelectItem value="title-asc">
                  <div className="flex items-center gap-2">
                    <SortAsc className="w-4 h-4" />
                    {t('problems.title_az')}
                  </div>
                </SelectItem>
                <SelectItem value="title-desc">
                  <div className="flex items-center gap-2">
                    <SortDesc className="w-4 h-4" />
                    {t('problems.title_za')}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Active Filters */}
          {(filters.search || filters.status || filters.urgency || filters.category || filters.sector) && (
            <div className="flex flex-wrap gap-2 pt-2 border-t">
              <span className="text-sm text-gray-600">{t('problems.active_filters')}:</span>
              {filters.search && (
                <Badge variant="outline" className="flex items-center gap-1">
                  {t('problems.search_placeholder').split(' ')[0]}: {filters.search}
                  <button onClick={() => setFilters(prev => ({ ...prev, search: '' }))}>
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}
              {filters.status && (
                <Badge variant="outline" className="flex items-center gap-1">
                  {t('problems.status')}: {STATUS_CONFIG[filters.status as keyof typeof STATUS_CONFIG].label}
                  <button onClick={() => setFilters(prev => ({ ...prev, status: '' }))}>
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}
              {filters.urgency && (
                <Badge variant="outline" className="flex items-center gap-1">
                  {t('problems.priority')}: {URGENCY_CONFIG[filters.urgency as keyof typeof URGENCY_CONFIG].label}
                  <button onClick={() => setFilters(prev => ({ ...prev, urgency: '' }))}>
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}
              {filters.category && (
                <Badge variant="outline" className="flex items-center gap-1">
                  {t('problems.category')}: {filters.category}
                  <button onClick={() => setFilters(prev => ({ ...prev, category: '' }))}>
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}
              {filters.sector && (
                <Badge variant="outline" className="flex items-center gap-1">
                  {t('problems.sector')}: {filters.sector}
                  <button onClick={() => setFilters(prev => ({ ...prev, sector: '' }))}>
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setFilters(prev => ({
                  ...prev,
                  search: '',
                  status: '',
                  urgency: '',
                  category: '',
                  sector: ''
                }))}
                className="text-red-600 hover:text-red-800"
              >
                {t('problems.clear_filters')}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <p className="text-gray-600">
            {loading ? t('problems.loading') : `${filteredProblems.length} ${t('problems.title').toLowerCase()}`}
          </p>
        </div>

        {loading ? (
          <div className="flex justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          </div>
        ) : filteredProblems.length === 0 ? (
          <EmptyState 
            message={
              filters.search || filters.status || filters.urgency || filters.category || filters.sector
                ? t('problems.no_results')
                : t('problems.no_problems_yet')
            }
          />
        ) : (
          <div className={`grid grid-cols-1 ${isMobile ? 'gap-4' : 'lg:grid-cols-2 gap-6'}`}>
            {filteredProblems.map(problem => (
              <ProblemCard key={problem.id} problem={problem} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}