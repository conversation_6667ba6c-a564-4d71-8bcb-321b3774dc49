import { useState, useEffect, useMemo, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { useLanguage } from '@/contexts/LanguageContext';
import { searchService } from '@/lib/search';
import { SearchFilters as SearchFiltersType } from '@/lib/search';
import { ProblemFilters } from './ProblemFilters';
import { ProblemList } from './ProblemList';
import { Problem } from '@/types';
import { Plus } from 'lucide-react';

interface Filters {
  search: string;
  status: string;
  urgency: string;
  category: string;
  sector: string;
  sortBy: 'created_at' | 'updated_at' | 'title';
  sortOrder: 'asc' | 'desc';
}

export function ProblemDashboard() {
  const [problems, setProblems] = useState<Problem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<Filters>({
    search: '',
    status: '',
    urgency: '',
    category: '',
    sector: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  
  const { toast } = useToast();
  const { user } = useAuthContext();
  const { t, isRTL } = useLanguage();

  // Memoized filter options to prevent unnecessary recalculations
  const categories = useMemo(() => 
    [...new Set(problems.map(p => p.category))].filter(Boolean), 
    [problems]
  );
  
  const sectors = useMemo(() => 
    [...new Set(problems.map(p => p.sector))].filter(Boolean), 
    [problems]
  );

  const fetchProblems = useCallback(async () => {
    setLoading(true);
    
    try {
      // Try to use search service for enhanced filtering
      if (filters.search.trim()) {
        const searchFilters: SearchFiltersType = {
          contentType: ['problem'],
          ...(filters.category && { categories: [filters.category] }),
          ...(filters.sector && { sectors: [filters.sector] }),
          ...(filters.status && { status: [filters.status] }),
          ...(filters.urgency && { urgency: [filters.urgency] })
        };

        const searchResults = await searchService.search({
          text: filters.search,
          filters: searchFilters,
          sortBy: filters.sortBy === 'created_at' ? 'date' : 
                  filters.sortBy === 'updated_at' ? 'date' : 'relevance',
          pagination: { limit: 50, offset: 0 },
          language: 'auto'
        });

        // Convert search results to Problem format
        const searchProblems = searchResults.items
          .filter(item => item.type === 'problem')
          .map(item => ({
            id: item.id,
            title: item.title,
            description: item.description,
            category: item.metadata?.category || 'غير محدد',
            sector: item.metadata?.sector || 'غير محدد',
            urgency: item.metadata?.urgency || 'medium',
            status: item.metadata?.status || 'open',
            submitted_by: item.metadata?.submitted_by || 'user',
            tags: item.tags || [],
            attachments: item.metadata?.attachments || [],
            created_at: item.metadata?.created_at || new Date().toISOString(),
            updated_at: item.metadata?.updated_at || new Date().toISOString(),
            users: {
              name: item.metadata?.submitter_name || 'مستخدم',
              organization: item.metadata?.organization
            },
            solutions: item.metadata?.solutions || []
          }));

        if (searchProblems.length > 0) {
          setProblems(searchProblems);
          setLoading(false);
          return;
        }
      }
    } catch (error) {
      console.warn('Search service not available, falling back to mock data:', error);
    }
    
    // Fallback to mock data
    const mockProblems: Problem[] = [
      {
        id: '1',
        title: 'تطوير نظام إدارة المستشفيات الرقمي',
        description: 'نحتاج إلى نظام شامل لإدارة المستشفيات يتضمن إدارة المرضى، الحجوزات، والمخزون الطبي',
        category: 'تطوير البرمجيات',
        sector: 'وزارة الصحة',
        urgency: 'high',
        status: 'open',
        submitted_by: 'user1',
        tags: ['نظم المعلومات الطبية', 'إدارة المستشفيات', 'قواعد البيانات'],
        attachments: [],
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        users: {
          name: 'د. أحمد الخطيب',
          organization: 'وزارة الصحة'
        },
        solutions: []
      },
      {
        id: '2',
        title: 'أتمتة عمليات التخليص الجمركي',
        description: 'نواجه تحديات في أتمتة عمليات التخليص الجمركي وربطها بالأنظمة المصرفية',
        category: 'أتمتة العمليات',
        sector: 'وزارة المالية',
        urgency: 'medium',
        status: 'in_progress',
        submitted_by: 'user2',
        tags: ['أتمتة', 'جمارك', 'تكامل الأنظمة'],
        attachments: [{ name: 'requirements.pdf', size: '2MB' }],
        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        users: {
          name: 'م. فاطمة السيد',
          organization: 'وزارة المالية'
        },
        solutions: [{ id: '1' }, { id: '2' }]
      },
      {
        id: '3',
        title: 'منصة التعليم الإلكتروني التفاعلي',
        description: 'تطوير منصة تعليم إلكتروني تفاعلية تدعم الفصول الافتراضية والتقييم الإلكتروني',
        category: 'تطوير المنصات',
        sector: 'وزارة التربية',
        urgency: 'critical',
        status: 'open',
        submitted_by: 'user3',
        tags: ['تعليم إلكتروني', 'فصول افتراضية', 'تقييم'],
        attachments: [],
        created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        users: {
          name: 'م. محمد العلي',
          organization: 'وزارة التربية'
        },
        solutions: []
      },
      {
        id: '4',
        title: 'نظام إدارة الموارد البشرية',
        description: 'نحتاج إلى نظام متكامل لإدارة الموارد البشرية يشمل الرواتب والإجازات والتقييم',
        category: 'أنظمة إدارية',
        sector: 'وزارة الإدارة المحلية',
        urgency: 'low',
        status: 'resolved',
        submitted_by: 'user4',
        tags: ['موارد بشرية', 'رواتب', 'إدارة'],
        attachments: [],
        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        users: {
          name: 'د. سارة أحمد',
          organization: 'وزارة الإدارة المحلية'
        },
        solutions: [{ id: '1' }]
      }
    ];
    
    setProblems(mockProblems);
    setLoading(false);
  }, [filters, t]);

  useEffect(() => {
    fetchProblems();
  }, [fetchProblems]);

  // Memoized filter and sort operations to prevent unnecessary recalculations
  const filteredProblems = useMemo(() => {
    return problems
      .filter(problem => {
        // Search filter
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          const matchesSearch = (
            problem.title.toLowerCase().includes(searchLower) ||
            problem.description.toLowerCase().includes(searchLower) ||
            problem.tags.some(tag => tag.toLowerCase().includes(searchLower))
          );
          if (!matchesSearch) return false;
        }

        // Status filter
        if (filters.status && problem.status !== filters.status) {
          return false;
        }

        // Urgency filter
        if (filters.urgency && problem.urgency !== filters.urgency) {
          return false;
        }

        // Category filter
        if (filters.category && problem.category !== filters.category) {
          return false;
        }

        // Sector filter
        if (filters.sector && problem.sector !== filters.sector) {
          return false;
        }

        return true;
      })
      .sort((a, b) => {
        const aValue = a[filters.sortBy];
        const bValue = b[filters.sortBy];
        
        if (filters.sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
  }, [problems, filters]);

  // Memoized callback for handling filter changes
  const handleFiltersChange = useCallback((newFilters: Filters) => {
    setFilters(newFilters);
  }, []);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return !!(filters.search || filters.status || filters.urgency || filters.category || filters.sector);
  }, [filters]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t('problems.title')}</h1>
          <p className="text-gray-600 mt-1">
            {t('problems.subtitle')}
          </p>
        </div>
        {user && (
          <Button asChild>
            <Link to="/problems/new">
              <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('problems.add_problem')}
            </Link>
          </Button>
        )}
      </div>

      {/* Filters */}
      <ProblemFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        categories={categories}
        sectors={sectors}
      />

      {/* Results */}
      <ProblemList
        problems={filteredProblems}
        loading={loading}
        hasFilters={hasActiveFilters}
      />
    </div>
  );
}