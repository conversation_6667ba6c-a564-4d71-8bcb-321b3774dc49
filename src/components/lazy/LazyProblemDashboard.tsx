import { lazy, Suspense } from 'react';
import { ProblemSkeleton } from '@/components/problems/ProblemSkeleton';

// Lazy load the ProblemDashboard component
const ProblemDashboard = lazy(() => 
  import('@/components/problems/ProblemDashboard').then(module => ({
    default: module.ProblemDashboard
  }))
);

export function LazyProblemDashboard(props: any) {
  return (
    <Suspense fallback={<ProblemSkeleton />}>
      <ProblemDashboard {...props} />
    </Suspense>
  );
}