import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { useAuthContext } from '@/components/auth/AuthProvider'
import { solutionOperations, problemOperations } from '@/lib/database'
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  ThumbsUp, 
  ThumbsDown, 
  Edit, 
  Trash2,
  Search,
  Filter,
  Calendar,
  Star,
  TrendingUp,
  MessageSquare,
  Loader2
} from 'lucide-react'

interface Solution {
  id: string
  problem_id: string
  expert_id: string
  content: string
  attachments: any[]
  status: 'draft' | 'submitted' | 'approved' | 'implemented'
  votes: any[]
  rating: number
  implementation_notes?: string
  created_at: string
  updated_at: string
  problems?: {
    id: string
    title: string
    category: string
    sector: string
    urgency: string
    status: string
  }
}

const SOLUTION_STATUS_CONFIG = {
  draft: { 
    label: 'مسودة', 
    color: 'bg-gray-100 text-gray-800',
    description: 'الحل لم يتم إرساله بعد'
  },
  submitted: { 
    label: 'مرسل', 
    color: 'bg-blue-100 text-blue-800',
    description: 'تم إرسال الحل وهو قيد المراجعة'
  },
  approved: { 
    label: 'معتمد', 
    color: 'bg-green-100 text-green-800',
    description: 'تم اعتماد الحل من قبل الإدارة'
  },
  implemented: { 
    label: 'مطبق', 
    color: 'bg-purple-100 text-purple-800',
    description: 'تم تطبيق الحل بنجاح'
  }
}

export function SolutionManagement() {
  const { user } = useAuthContext()
  const { toast } = useToast()
  const [solutions, setSolutions] = useState<Solution[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [editingSolution, setEditingSolution] = useState<Solution | null>(null)
  const [editContent, setEditContent] = useState('')

  useEffect(() => {
    if (user) {
      fetchSolutions()
    }
  }, [user])

  const fetchSolutions = async () => {
    if (!user) return

    setLoading(true)
    try {
      // This would need to be implemented in the database operations
      // For now, we'll use a mock implementation
      const { data, error } = await solutionOperations.getSolutionsForExpert?.(user.id) || { data: [], error: null }
      
      if (error) {
        throw new Error(error.message)
      }

      setSolutions(data || [])
    } catch (error) {
      console.error('Error fetching solutions:', error)
      toast({
        title: 'خطأ في تحميل البيانات',
        description: 'حدث خطأ أثناء تحميل حلولك',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredSolutions = solutions.filter(solution => {
    const matchesSearch = !searchQuery || 
      solution.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      solution.problems?.title.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || solution.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const handleEditSolution = (solution: Solution) => {
    setEditingSolution(solution)
    setEditContent(solution.content)
  }

  const handleSaveEdit = async () => {
    if (!editingSolution || !editContent.trim()) return

    try {
      const { error } = await solutionOperations.updateSolution(editingSolution.id, {
        content: editContent.trim(),
        updated_at: new Date().toISOString()
      })

      if (error) {
        throw new Error(error.message)
      }

      toast({
        title: 'تم التحديث بنجاح',
        description: 'تم تحديث الحل بنجاح'
      })

      setEditingSolution(null)
      setEditContent('')
      fetchSolutions()
    } catch (error) {
      console.error('Error updating solution:', error)
      toast({
        title: 'خطأ في التحديث',
        description: 'حدث خطأ أثناء تحديث الحل',
        variant: 'destructive'
      })
    }
  }

  const handleDeleteSolution = async (solutionId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا الحل؟')) return

    try {
      const { error } = await solutionOperations.softDeleteSolution(solutionId)

      if (error) {
        throw new Error(error.message)
      }

      toast({
        title: 'تم الحذف بنجاح',
        description: 'تم حذف الحل بنجاح'
      })

      fetchSolutions()
    } catch (error) {
      console.error('Error deleting solution:', error)
      toast({
        title: 'خطأ في الحذف',
        description: 'حدث خطأ أثناء حذف الحل',
        variant: 'destructive'
      })
    }
  }

  const getVoteCount = (votes: any[], type: 'up' | 'down') => {
    return votes?.filter(vote => vote.type === type).length || 0
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getSolutionStats = () => {
    const total = solutions.length
    const drafts = solutions.filter(s => s.status === 'draft').length
    const submitted = solutions.filter(s => s.status === 'submitted').length
    const approved = solutions.filter(s => s.status === 'approved').length
    const implemented = solutions.filter(s => s.status === 'implemented').length
    const totalVotes = solutions.reduce((sum, s) => sum + (s.votes?.length || 0), 0)
    const avgRating = solutions.length > 0 
      ? solutions.reduce((sum, s) => sum + s.rating, 0) / solutions.length 
      : 0

    return { total, drafts, submitted, approved, implemented, totalVotes, avgRating }
  }

  const stats = getSolutionStats()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">جاري تحميل حلولك...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">إدارة الحلول</h1>
        <p className="text-gray-600 mt-1">
          إدارة ومتابعة جميع الحلول التي قدمتها للمشاكل التقنية
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الحلول</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الحلول المعتمدة</p>
                <p className="text-2xl font-bold text-gray-900">{stats.approved + stats.implemented}</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي التصويتات</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalVotes}</p>
              </div>
              <div className="bg-yellow-100 p-3 rounded-full">
                <TrendingUp className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">متوسط التقييم</p>
                <p className="text-2xl font-bold text-gray-900">{stats.avgRating.toFixed(1)}</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Star className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">البحث والتصفية</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="ابحث في الحلول..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="تصفية حسب الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">جميع الحالات</SelectItem>
                {Object.entries(SOLUTION_STATUS_CONFIG).map(([key, config]) => (
                  <SelectItem key={key} value={key}>
                    {config.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Solutions List */}
      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">الكل ({stats.total})</TabsTrigger>
          <TabsTrigger value="draft">مسودات ({stats.drafts})</TabsTrigger>
          <TabsTrigger value="submitted">مرسلة ({stats.submitted})</TabsTrigger>
          <TabsTrigger value="approved">معتمدة ({stats.approved})</TabsTrigger>
          <TabsTrigger value="implemented">مطبقة ({stats.implemented})</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredSolutions.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  لا توجد حلول
                </h3>
                <p className="text-gray-600 mb-4">
                  لم تقم بتقديم أي حلول بعد
                </p>
                <Button asChild>
                  <Link to="/problems">
                    تصفح المشاكل
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredSolutions.map((solution) => (
                <Card key={solution.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <Badge className={SOLUTION_STATUS_CONFIG[solution.status].color}>
                            {SOLUTION_STATUS_CONFIG[solution.status].label}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {formatDate(solution.created_at)}
                          </span>
                        </div>
                        
                        <CardTitle className="text-lg mb-2">
                          <Link 
                            to={`/problems/${solution.problem_id}`}
                            className="hover:text-blue-600 transition-colors"
                          >
                            {solution.problems?.title || 'مشكلة محذوفة'}
                          </Link>
                        </CardTitle>
                        
                        {solution.problems && (
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span>{solution.problems.category}</span>
                            <span>{solution.problems.sector}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        {solution.status === 'draft' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditSolution(solution)}
                          >
                            <Edit className="w-4 h-4 ml-2" />
                            تعديل
                          </Button>
                        )}
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteSolution(solution.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="w-4 h-4 ml-2" />
                          حذف
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-4">
                      <div className="prose prose-sm max-w-none">
                        <p className="text-gray-700 line-clamp-3">
                          {solution.content}
                        </p>
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1 text-green-600">
                            <ThumbsUp className="w-4 h-4" />
                            {getVoteCount(solution.votes, 'up')}
                          </div>
                          <div className="flex items-center gap-1 text-red-600">
                            <ThumbsDown className="w-4 h-4" />
                            {getVoteCount(solution.votes, 'down')}
                          </div>
                          <div className="flex items-center gap-1 text-yellow-600">
                            <Star className="w-4 h-4" />
                            {solution.rating.toFixed(1)}
                          </div>
                        </div>

                        <div className="text-gray-500">
                          آخر تحديث: {formatDate(solution.updated_at)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Other tab contents would filter by status */}
        {Object.keys(SOLUTION_STATUS_CONFIG).map(status => (
          <TabsContent key={status} value={status} className="space-y-4">
            {filteredSolutions.filter(s => s.status === status).map((solution) => (
              <Card key={solution.id}>
                {/* Same card content as above */}
                <CardHeader>
                  <CardTitle className="text-lg">
                    <Link 
                      to={`/problems/${solution.problem_id}`}
                      className="hover:text-blue-600 transition-colors"
                    >
                      {solution.problems?.title || 'مشكلة محذوفة'}
                    </Link>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 line-clamp-3">
                    {solution.content}
                  </p>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        ))}
      </Tabs>

      {/* Edit Solution Modal */}
      {editingSolution && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>تعديل الحل</CardTitle>
              <CardDescription>
                قم بتعديل محتوى الحل قبل إرساله
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                rows={10}
                placeholder="اكتب الحل المحدث هنا..."
              />
              
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditingSolution(null)
                    setEditContent('')
                  }}
                >
                  إلغاء
                </Button>
                <Button
                  onClick={handleSaveEdit}
                  disabled={!editContent.trim()}
                >
                  حفظ التغييرات
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}