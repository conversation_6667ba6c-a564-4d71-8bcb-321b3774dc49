import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { problemOperations, solutionOperations } from '@/lib/database'
import { 
  Search, 
  Filter, 
  Eye, 
  Check, 
  X, 
  AlertTriangle, 
  Clock,
  FileText,
  MessageSquare,
  Flag,
  Trash2,
  MoreHorizontal,
  User
} from 'lucide-react'

interface ContentItem {
  id: string
  type: 'problem' | 'solution'
  title: string
  content: string
  author: string
  status: string
  created_at: string
  reports?: number
  flagged?: boolean
}

export function ContentModeration() {
  const { toast } = useToast()
  const [contentItems, setContentItems] = useState<ContentItem[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    status: '',
    sortBy: 'created_at'
  })

  useEffect(() => {
    loadContent()
  }, [filters])

  const loadContent = async () => {
    try {
      setLoading(true)
      
      // Load problems and solutions
      const [problemsResult, solutionsResult] = await Promise.all([
        problemOperations.getAllProblems({ includeDeleted: true }),
        // We'll need to add this method
        Promise.resolve({ data: [], error: null })
      ])

      if (problemsResult.error) {
        throw new Error('Failed to load content')
      }

      const problems = problemsResult.data || []
      
      // Transform problems to content items
      const problemItems: ContentItem[] = problems.map(problem => ({
        id: problem.id,
        type: 'problem',
        title: problem.title,
        content: problem.description,
        author: problem.users?.name || 'مستخدم غير معروف',
        status: problem.status,
        created_at: problem.created_at,
        reports: Math.floor(Math.random() * 5), // Mock data
        flagged: problem.is_deleted || Math.random() > 0.8
      }))

      // Apply filters
      let filteredItems = problemItems
      
      if (filters.search) {
        const searchLower = filters.search.toLowerCase()
        filteredItems = filteredItems.filter(item => 
          item.title.toLowerCase().includes(searchLower) ||
          item.content.toLowerCase().includes(searchLower) ||
          item.author.toLowerCase().includes(searchLower)
        )
      }

      if (filters.type) {
        filteredItems = filteredItems.filter(item => item.type === filters.type)
      }

      if (filters.status) {
        if (filters.status === 'flagged') {
          filteredItems = filteredItems.filter(item => item.flagged)
        } else if (filters.status === 'reported') {
          filteredItems = filteredItems.filter(item => (item.reports || 0) > 0)
        } else {
          filteredItems = filteredItems.filter(item => item.status === filters.status)
        }
      }

      // Sort
      filteredItems.sort((a, b) => {
        if (filters.sortBy === 'created_at') {
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        } else if (filters.sortBy === 'reports') {
          return (b.reports || 0) - (a.reports || 0)
        }
        return 0
      })

      setContentItems(filteredItems)
    } catch (error) {
      console.error('Error loading content:', error)
      toast({
        title: 'خطأ في تحميل المحتوى',
        description: 'حدث خطأ أثناء تحميل المحتوى للمراجعة',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async (itemId: string) => {
    try {
      // Implementation would depend on the content type
      toast({
        title: 'تم الموافقة',
        description: 'تم الموافقة على المحتوى بنجاح',
      })
      loadContent()
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء الموافقة على المحتوى',
        variant: 'destructive'
      })
    }
  }

  const handleReject = async (itemId: string) => {
    try {
      // Implementation would depend on the content type
      toast({
        title: 'تم الرفض',
        description: 'تم رفض المحتوى بنجاح',
      })
      loadContent()
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء رفض المحتوى',
        variant: 'destructive'
      })
    }
  }

  const handleDelete = async (itemId: string) => {
    try {
      // Implementation would use soft delete
      toast({
        title: 'تم الحذف',
        description: 'تم حذف المحتوى بنجاح',
      })
      loadContent()
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء حذف المحتوى',
        variant: 'destructive'
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status: string, flagged?: boolean, reports?: number) => {
    if (flagged) {
      return <Badge variant="destructive">مبلغ عنه</Badge>
    }
    if (reports && reports > 0) {
      return <Badge variant="secondary">{reports} تقارير</Badge>
    }
    
    switch (status) {
      case 'open':
        return <Badge variant="default">مفتوح</Badge>
      case 'in_progress':
        return <Badge variant="secondary">قيد المعالجة</Badge>
      case 'resolved':
        return <Badge variant="outline">محلول</Badge>
      case 'closed':
        return <Badge variant="outline">مغلق</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            تصفية المحتوى
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="البحث في المحتوى..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pr-10"
              />
            </div>

            <Select value={filters.type} onValueChange={(value) => setFilters(prev => ({ ...prev, type: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="نوع المحتوى" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">جميع الأنواع</SelectItem>
                <SelectItem value="problem">المشاكل</SelectItem>
                <SelectItem value="solution">الحلول</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">جميع الحالات</SelectItem>
                <SelectItem value="flagged">مبلغ عنه</SelectItem>
                <SelectItem value="reported">يحتوي تقارير</SelectItem>
                <SelectItem value="open">مفتوح</SelectItem>
                <SelectItem value="resolved">محلول</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.sortBy} onValueChange={(value) => setFilters(prev => ({ ...prev, sortBy: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="ترتيب حسب" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">تاريخ الإنشاء</SelectItem>
                <SelectItem value="reports">عدد التقارير</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Content List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل المحتوى...</p>
          </div>
        ) : contentItems.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد محتوى</h3>
              <p className="text-gray-600">لا يوجد محتوى يطابق معايير البحث</p>
            </CardContent>
          </Card>
        ) : (
          contentItems.map((item) => (
            <Card key={item.id} className={`${item.flagged ? 'border-red-200 bg-red-50' : ''}`}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      {item.type === 'problem' ? (
                        <FileText className="w-4 h-4 text-blue-600" />
                      ) : (
                        <MessageSquare className="w-4 h-4 text-green-600" />
                      )}
                      <Badge variant="outline" className="text-xs">
                        {item.type === 'problem' ? 'مشكلة' : 'حل'}
                      </Badge>
                      {getStatusBadge(item.status, item.flagged, item.reports)}
                      {item.flagged && (
                        <Flag className="w-4 h-4 text-red-500" />
                      )}
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                      {item.title}
                    </h3>

                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {item.content}
                    </p>

                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        {item.author}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {formatDate(item.created_at)}
                      </div>
                      {item.reports && item.reports > 0 && (
                        <div className="flex items-center gap-1 text-red-600">
                          <AlertTriangle className="w-3 h-3" />
                          {item.reports} تقارير
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4" />
                    </Button>
                    
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="text-green-600 hover:text-green-700"
                      onClick={() => handleApprove(item.id)}
                    >
                      <Check className="w-4 h-4" />
                    </Button>
                    
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="text-red-600 hover:text-red-700"
                      onClick={() => handleReject(item.id)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                    
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="text-red-600 hover:text-red-700"
                      onClick={() => handleDelete(item.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}