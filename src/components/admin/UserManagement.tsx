import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useToast } from '@/hooks/use-toast'
import { userOperations, expertOperations } from '@/lib/database'
import { 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2, 
  UserCheck, 
  UserX,
  Shield,
  Clock,
  MapPin,
  Mail,
  Phone,
  Building,
  MoreHorizontal,
  Ban,
  CheckCircle
} from 'lucide-react'

interface UserWithExpert {
  id: string
  email: string
  name: string
  role: 'expert' | 'ministry_user' | 'admin'
  avatar?: string
  bio?: string
  location: string
  phone_number?: string
  organization?: string
  position?: string
  is_active: boolean
  is_deleted: boolean
  created_at: string
  last_login_at?: string
  expert_profile?: {
    rating: number
    total_contributions: number
    availability: string
    experience_years: number
  }
}

export function UserManagement() {
  const { toast } = useToast()
  const [users, setUsers] = useState<UserWithExpert[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    search: '',
    role: '',
    status: '',
    sortBy: 'created_at'
  })

  useEffect(() => {
    loadUsers()
  }, [filters])

  const loadUsers = async () => {
    try {
      setLoading(true)
      
      // Load users and experts
      const [usersResult, expertsResult] = await Promise.all([
        userOperations.getAllUsers({ 
          role: filters.role || undefined,
          includeDeleted: true 
        }),
        expertOperations.getAllExperts({ includeDeleted: true })
      ])

      if (usersResult.error) {
        throw new Error('Failed to load users')
      }

      const usersData = usersResult.data || []
      const expertsData = expertsResult.data || []

      // Merge user data with expert profiles
      const usersWithExperts: UserWithExpert[] = usersData.map(user => {
        const expertProfile = expertsData.find(expert => expert.user_id === user.id)
        return {
          ...user,
          expert_profile: expertProfile ? {
            rating: expertProfile.rating,
            total_contributions: expertProfile.total_contributions,
            availability: expertProfile.availability,
            experience_years: expertProfile.experience_years
          } : undefined
        }
      })

      // Apply filters
      let filteredUsers = usersWithExperts

      if (filters.search) {
        const searchLower = filters.search.toLowerCase()
        filteredUsers = filteredUsers.filter(user => 
          user.name.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower) ||
          user.organization?.toLowerCase().includes(searchLower) ||
          user.location.toLowerCase().includes(searchLower)
        )
      }

      if (filters.status) {
        if (filters.status === 'active') {
          filteredUsers = filteredUsers.filter(user => user.is_active && !user.is_deleted)
        } else if (filters.status === 'inactive') {
          filteredUsers = filteredUsers.filter(user => !user.is_active)
        } else if (filters.status === 'deleted') {
          filteredUsers = filteredUsers.filter(user => user.is_deleted)
        }
      }

      // Sort
      filteredUsers.sort((a, b) => {
        if (filters.sortBy === 'created_at') {
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        } else if (filters.sortBy === 'name') {
          return a.name.localeCompare(b.name, 'ar')
        } else if (filters.sortBy === 'last_login') {
          const aLogin = a.last_login_at ? new Date(a.last_login_at).getTime() : 0
          const bLogin = b.last_login_at ? new Date(b.last_login_at).getTime() : 0
          return bLogin - aLogin
        }
        return 0
      })

      setUsers(filteredUsers)
    } catch (error) {
      console.error('Error loading users:', error)
      toast({
        title: 'خطأ في تحميل المستخدمين',
        description: 'حدث خطأ أثناء تحميل قائمة المستخدمين',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleActivateUser = async (userId: string) => {
    try {
      // Implementation would update user status
      toast({
        title: 'تم التفعيل',
        description: 'تم تفعيل المستخدم بنجاح',
      })
      loadUsers()
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء تفعيل المستخدم',
        variant: 'destructive'
      })
    }
  }

  const handleDeactivateUser = async (userId: string) => {
    try {
      // Implementation would update user status
      toast({
        title: 'تم إلغاء التفعيل',
        description: 'تم إلغاء تفعيل المستخدم بنجاح',
      })
      loadUsers()
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء إلغاء تفعيل المستخدم',
        variant: 'destructive'
      })
    }
  }

  const handleDeleteUser = async (userId: string) => {
    try {
      await userOperations.softDeleteUser(userId)
      toast({
        title: 'تم الحذف',
        description: 'تم حذف المستخدم بنجاح',
      })
      loadUsers()
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء حذف المستخدم',
        variant: 'destructive'
      })
    }
  }

  const handleRestoreUser = async (userId: string) => {
    try {
      await userOperations.restoreUser(userId)
      toast({
        title: 'تم الاستعادة',
        description: 'تم استعادة المستخدم بنجاح',
      })
      loadUsers()
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء استعادة المستخدم',
        variant: 'destructive'
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge variant="destructive">مدير</Badge>
      case 'expert':
        return <Badge variant="default">خبير</Badge>
      case 'ministry_user':
        return <Badge variant="secondary">موظف وزارة</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  const getStatusBadge = (user: UserWithExpert) => {
    if (user.is_deleted) {
      return <Badge variant="destructive">محذوف</Badge>
    }
    if (!user.is_active) {
      return <Badge variant="secondary">غير نشط</Badge>
    }
    return <Badge variant="outline" className="text-green-600">نشط</Badge>
  }

  const getUserInitials = (name: string) => {
    const parts = name.split(' ')
    return parts.length >= 2 ? `${parts[0][0]}${parts[1][0]}` : name[0]
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            تصفية المستخدمين
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="البحث في المستخدمين..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pr-10"
              />
            </div>

            <Select value={filters.role} onValueChange={(value) => setFilters(prev => ({ ...prev, role: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="الدور" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">جميع الأدوار</SelectItem>
                <SelectItem value="admin">مدير</SelectItem>
                <SelectItem value="expert">خبير</SelectItem>
                <SelectItem value="ministry_user">موظف وزارة</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">جميع الحالات</SelectItem>
                <SelectItem value="active">نشط</SelectItem>
                <SelectItem value="inactive">غير نشط</SelectItem>
                <SelectItem value="deleted">محذوف</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.sortBy} onValueChange={(value) => setFilters(prev => ({ ...prev, sortBy: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="ترتيب حسب" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">تاريخ التسجيل</SelectItem>
                <SelectItem value="name">الاسم</SelectItem>
                <SelectItem value="last_login">آخر دخول</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل المستخدمين...</p>
          </div>
        ) : users.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <UserCheck className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد مستخدمين</h3>
              <p className="text-gray-600">لا يوجد مستخدمين يطابقون معايير البحث</p>
            </CardContent>
          </Card>
        ) : (
          users.map((user) => (
            <Card key={user.id} className={`${user.is_deleted ? 'border-red-200 bg-red-50' : ''}`}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex items-start gap-4 flex-1">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={user.avatar} alt={user.name} />
                      <AvatarFallback className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-bold">
                        {getUserInitials(user.name)}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {user.name}
                        </h3>
                        {getRoleBadge(user.role)}
                        {getStatusBadge(user)}
                        {user.role === 'admin' && (
                          <Shield className="w-4 h-4 text-red-500" />
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mb-3">
                        <div className="flex items-center gap-1">
                          <Mail className="w-3 h-3" />
                          {user.email}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {user.location}
                        </div>
                        {user.phone_number && (
                          <div className="flex items-center gap-1">
                            <Phone className="w-3 h-3" />
                            {user.phone_number}
                          </div>
                        )}
                        {user.organization && (
                          <div className="flex items-center gap-1">
                            <Building className="w-3 h-3" />
                            {user.organization}
                          </div>
                        )}
                      </div>

                      {user.expert_profile && (
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                          <span>التقييم: {user.expert_profile.rating.toFixed(1)}/5</span>
                          <span>المساهمات: {user.expert_profile.total_contributions}</span>
                          <span>الخبرة: {user.expert_profile.experience_years} سنة</span>
                          <Badge variant="outline" className="text-xs">
                            {user.expert_profile.availability === 'available' ? 'متاح' : 
                             user.expert_profile.availability === 'busy' ? 'مشغول' : 'غير متاح'}
                          </Badge>
                        </div>
                      )}

                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          انضم في {formatDate(user.created_at)}
                        </div>
                        {user.last_login_at && (
                          <div className="flex items-center gap-1">
                            <UserCheck className="w-3 h-3" />
                            آخر دخول: {formatDate(user.last_login_at)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4" />
                    </Button>
                    
                    <Button size="sm" variant="outline">
                      <Edit className="w-4 h-4" />
                    </Button>
                    
                    {user.is_deleted ? (
                      <Button 
                        size="sm" 
                        variant="outline"
                        className="text-green-600 hover:text-green-700"
                        onClick={() => handleRestoreUser(user.id)}
                      >
                        <CheckCircle className="w-4 h-4" />
                      </Button>
                    ) : (
                      <>
                        {user.is_active ? (
                          <Button 
                            size="sm" 
                            variant="outline"
                            className="text-orange-600 hover:text-orange-700"
                            onClick={() => handleDeactivateUser(user.id)}
                          >
                            <Ban className="w-4 h-4" />
                          </Button>
                        ) : (
                          <Button 
                            size="sm" 
                            variant="outline"
                            className="text-green-600 hover:text-green-700"
                            onClick={() => handleActivateUser(user.id)}
                          >
                            <UserCheck className="w-4 h-4" />
                          </Button>
                        )}
                        
                        <Button 
                          size="sm" 
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                          onClick={() => handleDeleteUser(user.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}