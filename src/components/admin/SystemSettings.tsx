import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { 
  Settings, 
  Save, 
  Mail, 
  Bell, 
  Shield, 
  Database,
  Globe,
  Palette,
  Server,
  Key,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'

interface SystemSettings {
  general: {
    siteName: string
    siteDescription: string
    contactEmail: string
    supportEmail: string
    defaultLanguage: string
    timezone: string
  }
  notifications: {
    emailNotifications: boolean
    pushNotifications: boolean
    smsNotifications: boolean
    adminAlerts: boolean
    userWelcomeEmail: boolean
    problemStatusUpdates: boolean
  }
  security: {
    requireEmailVerification: boolean
    enableTwoFactor: boolean
    passwordMinLength: number
    sessionTimeout: number
    maxLoginAttempts: number
    enableCaptcha: boolean
  }
  content: {
    autoModeration: boolean
    requireApproval: boolean
    allowFileUploads: boolean
    maxFileSize: number
    allowedFileTypes: string[]
    contentRetentionDays: number
  }
}

export function SystemSettings() {
  const { toast } = useToast()
  const [settings, setSettings] = useState<SystemSettings>({
    general: {
      siteName: 'مركز سوريا الذكي',
      siteDescription: 'منصة تجمع الخبراء والحلول التقنية لخدمة المجتمع السوري',
      contactEmail: '<EMAIL>',
      supportEmail: '<EMAIL>',
      defaultLanguage: 'ar',
      timezone: 'Asia/Damascus'
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      adminAlerts: true,
      userWelcomeEmail: true,
      problemStatusUpdates: true
    },
    security: {
      requireEmailVerification: true,
      enableTwoFactor: false,
      passwordMinLength: 8,
      sessionTimeout: 24,
      maxLoginAttempts: 5,
      enableCaptcha: true
    },
    content: {
      autoModeration: true,
      requireApproval: false,
      allowFileUploads: true,
      maxFileSize: 10,
      allowedFileTypes: ['pdf', 'doc', 'docx', 'jpg', 'png', 'gif'],
      contentRetentionDays: 365
    }
  })
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('general')

  const handleSave = async (section: keyof SystemSettings) => {
    try {
      setLoading(true)
      
      // In real implementation, this would save to database
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: 'تم الحفظ بنجاح',
        description: 'تم حفظ إعدادات النظام بنجاح',
      })
    } catch (error) {
      toast({
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ أثناء حفظ الإعدادات',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const updateSetting = (section: keyof SystemSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }))
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            عام
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="w-4 h-4" />
            الإشعارات
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            الأمان
          </TabsTrigger>
          <TabsTrigger value="content" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            المحتوى
          </TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                الإعدادات العامة
              </CardTitle>
              <CardDescription>
                إعدادات عامة للموقع والمنصة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="siteName">اسم الموقع</Label>
                  <Input
                    id="siteName"
                    value={settings.general.siteName}
                    onChange={(e) => updateSetting('general', 'siteName', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactEmail">البريد الإلكتروني للتواصل</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={settings.general.contactEmail}
                    onChange={(e) => updateSetting('general', 'contactEmail', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="supportEmail">البريد الإلكتروني للدعم</Label>
                  <Input
                    id="supportEmail"
                    type="email"
                    value={settings.general.supportEmail}
                    onChange={(e) => updateSetting('general', 'supportEmail', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="defaultLanguage">اللغة الافتراضية</Label>
                  <Select 
                    value={settings.general.defaultLanguage} 
                    onValueChange={(value) => updateSetting('general', 'defaultLanguage', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ar">العربية</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timezone">المنطقة الزمنية</Label>
                  <Select 
                    value={settings.general.timezone} 
                    onValueChange={(value) => updateSetting('general', 'timezone', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Asia/Damascus">دمشق</SelectItem>
                      <SelectItem value="Asia/Riyadh">الرياض</SelectItem>
                      <SelectItem value="Asia/Dubai">دبي</SelectItem>
                      <SelectItem value="Europe/London">لندن</SelectItem>
                      <SelectItem value="America/New_York">نيويورك</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="siteDescription">وصف الموقع</Label>
                <Textarea
                  id="siteDescription"
                  rows={3}
                  value={settings.general.siteDescription}
                  onChange={(e) => updateSetting('general', 'siteDescription', e.target.value)}
                />
              </div>

              <Button onClick={() => handleSave('general')} disabled={loading}>
                <Save className="w-4 h-4 ml-2" />
                {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Settings */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                إعدادات الإشعارات
              </CardTitle>
              <CardDescription>
                إدارة الإشعارات والتنبيهات
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="emailNotifications">الإشعارات عبر البريد الإلكتروني</Label>
                    <p className="text-sm text-gray-600">إرسال إشعارات عبر البريد الإلكتروني</p>
                  </div>
                  <Switch
                    id="emailNotifications"
                    checked={settings.notifications.emailNotifications}
                    onCheckedChange={(checked) => updateSetting('notifications', 'emailNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="pushNotifications">الإشعارات الفورية</Label>
                    <p className="text-sm text-gray-600">إشعارات فورية في المتصفح</p>
                  </div>
                  <Switch
                    id="pushNotifications"
                    checked={settings.notifications.pushNotifications}
                    onCheckedChange={(checked) => updateSetting('notifications', 'pushNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="smsNotifications">الإشعارات عبر الرسائل النصية</Label>
                    <p className="text-sm text-gray-600">إرسال إشعارات عبر SMS</p>
                  </div>
                  <Switch
                    id="smsNotifications"
                    checked={settings.notifications.smsNotifications}
                    onCheckedChange={(checked) => updateSetting('notifications', 'smsNotifications', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="adminAlerts">تنبيهات المدير</Label>
                    <p className="text-sm text-gray-600">تنبيهات خاصة بالمديرين</p>
                  </div>
                  <Switch
                    id="adminAlerts"
                    checked={settings.notifications.adminAlerts}
                    onCheckedChange={(checked) => updateSetting('notifications', 'adminAlerts', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="userWelcomeEmail">رسالة ترحيب للمستخدمين الجدد</Label>
                    <p className="text-sm text-gray-600">إرسال رسالة ترحيب عند التسجيل</p>
                  </div>
                  <Switch
                    id="userWelcomeEmail"
                    checked={settings.notifications.userWelcomeEmail}
                    onCheckedChange={(checked) => updateSetting('notifications', 'userWelcomeEmail', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="problemStatusUpdates">تحديثات حالة المشاكل</Label>
                    <p className="text-sm text-gray-600">إشعارات عند تغيير حالة المشكلة</p>
                  </div>
                  <Switch
                    id="problemStatusUpdates"
                    checked={settings.notifications.problemStatusUpdates}
                    onCheckedChange={(checked) => updateSetting('notifications', 'problemStatusUpdates', checked)}
                  />
                </div>
              </div>

              <Button onClick={() => handleSave('notifications')} disabled={loading}>
                <Save className="w-4 h-4 ml-2" />
                {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                إعدادات الأمان
              </CardTitle>
              <CardDescription>
                إعدادات الأمان وحماية النظام
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="requireEmailVerification">تأكيد البريد الإلكتروني مطلوب</Label>
                    <p className="text-sm text-gray-600">يجب تأكيد البريد الإلكتروني عند التسجيل</p>
                  </div>
                  <Switch
                    id="requireEmailVerification"
                    checked={settings.security.requireEmailVerification}
                    onCheckedChange={(checked) => updateSetting('security', 'requireEmailVerification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableTwoFactor">تفعيل المصادقة الثنائية</Label>
                    <p className="text-sm text-gray-600">مصادقة ثنائية للحسابات الحساسة</p>
                  </div>
                  <Switch
                    id="enableTwoFactor"
                    checked={settings.security.enableTwoFactor}
                    onCheckedChange={(checked) => updateSetting('security', 'enableTwoFactor', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableCaptcha">تفعيل الكابتشا</Label>
                    <p className="text-sm text-gray-600">حماية من الروبوتات والهجمات الآلية</p>
                  </div>
                  <Switch
                    id="enableCaptcha"
                    checked={settings.security.enableCaptcha}
                    onCheckedChange={(checked) => updateSetting('security', 'enableCaptcha', checked)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="passwordMinLength">الحد الأدنى لطول كلمة المرور</Label>
                  <Input
                    id="passwordMinLength"
                    type="number"
                    min="6"
                    max="20"
                    value={settings.security.passwordMinLength}
                    onChange={(e) => updateSetting('security', 'passwordMinLength', parseInt(e.target.value))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">انتهاء الجلسة (ساعة)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    min="1"
                    max="168"
                    value={settings.security.sessionTimeout}
                    onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxLoginAttempts">الحد الأقصى لمحاولات تسجيل الدخول</Label>
                  <Input
                    id="maxLoginAttempts"
                    type="number"
                    min="3"
                    max="10"
                    value={settings.security.maxLoginAttempts}
                    onChange={(e) => updateSetting('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <Button onClick={() => handleSave('security')} disabled={loading}>
                <Save className="w-4 h-4 ml-2" />
                {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Settings */}
        <TabsContent value="content">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="w-5 h-5" />
                إعدادات المحتوى
              </CardTitle>
              <CardDescription>
                إدارة المحتوى والملفات المرفوعة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="autoModeration">المراجعة التلقائية</Label>
                    <p className="text-sm text-gray-600">مراجعة تلقائية للمحتوى المرفوع</p>
                  </div>
                  <Switch
                    id="autoModeration"
                    checked={settings.content.autoModeration}
                    onCheckedChange={(checked) => updateSetting('content', 'autoModeration', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="requireApproval">الموافقة مطلوبة</Label>
                    <p className="text-sm text-gray-600">يتطلب موافقة المدير قبل النشر</p>
                  </div>
                  <Switch
                    id="requireApproval"
                    checked={settings.content.requireApproval}
                    onCheckedChange={(checked) => updateSetting('content', 'requireApproval', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="allowFileUploads">السماح برفع الملفات</Label>
                    <p className="text-sm text-gray-600">السماح للمستخدمين برفع الملفات</p>
                  </div>
                  <Switch
                    id="allowFileUploads"
                    checked={settings.content.allowFileUploads}
                    onCheckedChange={(checked) => updateSetting('content', 'allowFileUploads', checked)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxFileSize">الحد الأقصى لحجم الملف (MB)</Label>
                  <Input
                    id="maxFileSize"
                    type="number"
                    min="1"
                    max="100"
                    value={settings.content.maxFileSize}
                    onChange={(e) => updateSetting('content', 'maxFileSize', parseInt(e.target.value))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contentRetentionDays">مدة الاحتفاظ بالمحتوى (يوم)</Label>
                  <Input
                    id="contentRetentionDays"
                    type="number"
                    min="30"
                    max="3650"
                    value={settings.content.contentRetentionDays}
                    onChange={(e) => updateSetting('content', 'contentRetentionDays', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>أنواع الملفات المسموحة</Label>
                <div className="flex flex-wrap gap-2">
                  {['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'gif', 'mp4', 'mp3'].map((type) => (
                    <div key={type} className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="checkbox"
                        id={type}
                        checked={settings.content.allowedFileTypes.includes(type)}
                        onChange={(e) => {
                          const newTypes = e.target.checked
                            ? [...settings.content.allowedFileTypes, type]
                            : settings.content.allowedFileTypes.filter(t => t !== type)
                          updateSetting('content', 'allowedFileTypes', newTypes)
                        }}
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor={type} className="text-sm">
                        .{type}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <Button onClick={() => handleSave('content')} disabled={loading}>
                <Save className="w-4 h-4 ml-2" />
                {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            حالة النظام
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">قاعدة البيانات</p>
                <p className="text-sm text-green-600">متصلة</p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">خدمة البريد الإلكتروني</p>
                <p className="text-sm text-green-600">تعمل بشكل طبيعي</p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">النسخ الاحتياطي</p>
                <p className="text-sm text-yellow-600">آخر نسخة: منذ 6 ساعات</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}