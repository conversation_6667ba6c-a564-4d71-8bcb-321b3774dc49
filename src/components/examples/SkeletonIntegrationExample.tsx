import { useState, useEffect } from 'react'
import { LoadingStateManager, useLoadingState } from '@/components/common/LoadingStateManager'
import { ProblemDashboardSkeleton, ProblemCardSkeleton } from '@/components/problems/ProblemSkeleton'
import { ExpertDirectorySkeleton, ExpertCardSkeleton } from '@/components/experts/ExpertSkeleton'
import { SearchResultsSkeleton } from '@/components/search/SearchSkeleton'
import { CardSkeleton, ListSkeleton, FormSkeleton } from '@/components/ui/skeleton-variants'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// Example 1: Problem Dashboard with Skeleton Loading
export function ProblemDashboardWithSkeleton() {
  const { isLoading, startLoading, stopLoading } = useLoadingState(true)
  const [problems, setProblems] = useState([])

  useEffect(() => {
    // Simulate data loading
    const loadProblems = async () => {
      startLoading()
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock data
      setProblems([
        { id: '1', title: 'Sample Problem 1', description: 'Description...' },
        { id: '2', title: 'Sample Problem 2', description: 'Description...' }
      ])
      
      stopLoading()
    }

    loadProblems()
  }, [])

  return (
    <LoadingStateManager
      isLoading={isLoading}
      loadingComponent={<ProblemDashboardSkeleton itemCount={6} animation="wave" />}
      fadeTransition={true}
      transitionDuration={400}
      minLoadingTime={800}
    >
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Problems Dashboard</h1>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {problems.map((problem: any) => (
            <Card key={problem.id}>
              <CardHeader>
                <CardTitle>{problem.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{problem.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </LoadingStateManager>
  )
}

// Example 2: Expert Directory with Skeleton Loading
export function ExpertDirectoryWithSkeleton() {
  const { isLoading, startLoading, stopLoading } = useLoadingState(true)
  const [experts, setExperts] = useState([])

  useEffect(() => {
    const loadExperts = async () => {
      startLoading()
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      setExperts([
        { id: '1', name: 'Expert 1', expertise: 'AI/ML' },
        { id: '2', name: 'Expert 2', expertise: 'Web Development' }
      ])
      
      stopLoading()
    }

    loadExperts()
  }, [])

  return (
    <LoadingStateManager
      isLoading={isLoading}
      loadingComponent={<ExpertDirectorySkeleton itemCount={9} animation="pulse" />}
      fadeTransition={true}
      slideTransition={true}
    >
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Expert Directory</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {experts.map((expert: any) => (
            <Card key={expert.id}>
              <CardHeader>
                <CardTitle>{expert.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{expert.expertise}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </LoadingStateManager>
  )
}

// Example 3: Search Results with Skeleton Loading
export function SearchResultsWithSkeleton() {
  const { isLoading, startLoading, stopLoading } = useLoadingState(false)
  const [results, setResults] = useState([])
  const [query, setQuery] = useState('')

  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) return
    
    startLoading()
    setQuery(searchQuery)
    
    // Simulate search API call
    await new Promise(resolve => setTimeout(resolve, 1200))
    
    setResults([
      { id: '1', title: 'Search Result 1', type: 'problem' },
      { id: '2', title: 'Search Result 2', type: 'expert' },
      { id: '3', title: 'Search Result 3', type: 'solution' }
    ])
    
    stopLoading()
  }

  return (
    <div className="space-y-6">
      <div className="flex gap-2">
        <input
          type="text"
          placeholder="Search..."
          className="flex-1 px-3 py-2 border rounded-md"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSearch(e.currentTarget.value)
            }
          }}
        />
        <Button onClick={() => handleSearch(query)}>
          Search
        </Button>
      </div>

      <LoadingStateManager
        isLoading={isLoading}
        loadingComponent={
          <SearchResultsSkeleton 
            resultCount={5} 
            showTabs={true} 
            animation="shimmer" 
          />
        }
        fadeTransition={true}
        transitionDuration={300}
      >
        <div className="space-y-4">
          {results.length > 0 ? (
            results.map((result: any) => (
              <Card key={result.id}>
                <CardHeader>
                  <CardTitle>{result.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>Type: {result.type}</p>
                </CardContent>
              </Card>
            ))
          ) : (
            <p className="text-gray-500 text-center py-8">
              No results found. Try searching for something.
            </p>
          )}
        </div>
      </LoadingStateManager>
    </div>
  )
}

// Example 4: Individual Card Skeletons
export function CardSkeletonExamples() {
  const [loadingStates, setLoadingStates] = useState({
    problem: false,
    expert: false,
    generic: false
  })

  const toggleLoading = (type: keyof typeof loadingStates) => {
    setLoadingStates(prev => ({ ...prev, [type]: !prev[type] }))
  }

  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold">Individual Skeleton Examples</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Problem Card */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Problem Card</h3>
            <Button 
              size="sm" 
              onClick={() => toggleLoading('problem')}
            >
              {loadingStates.problem ? 'Show Content' : 'Show Skeleton'}
            </Button>
          </div>
          
          <LoadingStateManager
            isLoading={loadingStates.problem}
            loadingComponent={<ProblemCardSkeleton animation="wave" />}
            fadeTransition={true}
          >
            <Card>
              <CardHeader>
                <CardTitle>Sample Problem</CardTitle>
              </CardHeader>
              <CardContent>
                <p>This is a sample problem description that would normally be loaded from an API.</p>
                <div className="flex gap-2 mt-4">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                    Open
                  </span>
                  <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-sm">
                    High Priority
                  </span>
                </div>
              </CardContent>
            </Card>
          </LoadingStateManager>
        </div>

        {/* Expert Card */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Expert Card</h3>
            <Button 
              size="sm" 
              onClick={() => toggleLoading('expert')}
            >
              {loadingStates.expert ? 'Show Content' : 'Show Skeleton'}
            </Button>
          </div>
          
          <LoadingStateManager
            isLoading={loadingStates.expert}
            loadingComponent={<ExpertCardSkeleton animation="pulse" />}
            fadeTransition={true}
          >
            <Card>
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-2" />
                <CardTitle>Dr. Jane Smith</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-center text-gray-600 mb-4">AI/ML Specialist</p>
                <div className="flex justify-between text-sm">
                  <span>5 years experience</span>
                  <span>⭐ 4.8</span>
                </div>
              </CardContent>
            </Card>
          </LoadingStateManager>
        </div>

        {/* Generic Card */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Generic Card</h3>
            <Button 
              size="sm" 
              onClick={() => toggleLoading('generic')}
            >
              {loadingStates.generic ? 'Show Content' : 'Show Skeleton'}
            </Button>
          </div>
          
          <LoadingStateManager
            isLoading={loadingStates.generic}
            loadingComponent={
              <CardSkeleton 
                rows={3} 
                avatar={true} 
                animation="shimmer" 
                showActions={true}
              />
            }
            fadeTransition={true}
          >
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-500 rounded-full" />
                  <div>
                    <CardTitle>Generic Content</CardTitle>
                    <p className="text-sm text-gray-600">Subtitle</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p>This is generic content that demonstrates the flexible skeleton system.</p>
                <div className="flex justify-between items-center mt-4 pt-4 border-t">
                  <span className="text-sm text-gray-500">2 hours ago</span>
                  <Button size="sm">Action</Button>
                </div>
              </CardContent>
            </Card>
          </LoadingStateManager>
        </div>
      </div>
    </div>
  )
}

// Example 5: Form Skeleton
export function FormSkeletonExample() {
  const { isLoading, startLoading, stopLoading } = useLoadingState(false)

  const simulateFormLoad = async () => {
    startLoading()
    await new Promise(resolve => setTimeout(resolve, 1500))
    stopLoading()
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Form Skeleton Example</h2>
        <Button onClick={simulateFormLoad}>
          {isLoading ? 'Loading...' : 'Simulate Form Load'}
        </Button>
      </div>

      <LoadingStateManager
        isLoading={isLoading}
        loadingComponent={
          <FormSkeleton 
            fields={6}
            fieldTypes={['input', 'textarea', 'select', 'input', 'checkbox', 'input']}
            animation="wave"
            showSubmit={true}
          />
        }
        fadeTransition={true}
        transitionDuration={400}
      >
        <form className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Name</label>
            <input type="text" className="w-full px-3 py-2 border rounded-md" />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Description</label>
            <textarea className="w-full px-3 py-2 border rounded-md h-24" />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Category</label>
            <select className="w-full px-3 py-2 border rounded-md">
              <option>Select category</option>
            </select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Email</label>
            <input type="email" className="w-full px-3 py-2 border rounded-md" />
          </div>
          
          <div className="flex items-center gap-2">
            <input type="checkbox" />
            <label className="text-sm">I agree to the terms</label>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Phone</label>
            <input type="tel" className="w-full px-3 py-2 border rounded-md" />
          </div>
          
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline">Cancel</Button>
            <Button>Submit</Button>
          </div>
        </form>
      </LoadingStateManager>
    </div>
  )
}

// Main demo component
export function SkeletonIntegrationDemo() {
  return (
    <div className="space-y-12 p-6">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Skeleton Loading Components Demo</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          This demo showcases the comprehensive skeleton loading system with smooth transitions 
          and various animation options for different UI components.
        </p>
      </div>

      <ProblemDashboardWithSkeleton />
      <ExpertDirectoryWithSkeleton />
      <SearchResultsWithSkeleton />
      <CardSkeletonExamples />
      <FormSkeletonExample />
    </div>
  )
}