import { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Separator } from '@/components/ui/separator'
import { useDeviceType, useTouchSupport } from '@/hooks/use-mobile'
import { SearchFilters as SearchFiltersType, ContentType } from '@/lib/search/types'
import { 
  Filter, 
  X, 
  ChevronDown, 
  ChevronUp,
  Calendar as CalendarIcon,
  MapPin,
  Star,
  Tag,
  FileText,
  User,
  Lightbulb,
  Video
} from 'lucide-react'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

interface SearchFiltersProps {
  filters: SearchFiltersType
  onFiltersChange: (filters: SearchFiltersType) => void
  onClearFilters: () => void
  availableCategories?: string[]
  availableSectors?: string[]
  availableLocations?: string[]
  className?: string
  onSavePreset?: (name: string, filters: SearchFiltersType) => void
  onLoadPreset?: (filters: SearchFiltersType) => void
  savedPresets?: Array<{ name: string; filters: SearchFiltersType }>
}

// Predefined sector options based on requirements
const SECTORS = [
  'technology',
  'agriculture', 
  'energy',
  'finance',
  'industry',
  'management',
  'help&catastrophe',
  'economics',
  'healthcare',
  'education',
  'infrastructure'
]

const SECTOR_LABELS: Record<string, string> = {
  technology: 'التكنولوجيا',
  agriculture: 'الزراعة',
  energy: 'الطاقة',
  finance: 'المالية',
  industry: 'الصناعة',
  management: 'الإدارة',
  'help&catastrophe': 'المساعدة والكوارث',
  economics: 'الاقتصاد',
  healthcare: 'الصحة',
  education: 'التعليم',
  infrastructure: 'البنية التحتية'
}

const STATUS_OPTIONS = {
  problem: [
    { value: 'open', label: 'مفتوحة' },
    { value: 'in_progress', label: 'قيد المعالجة' },
    { value: 'resolved', label: 'محلولة' },
    { value: 'closed', label: 'مغلقة' }
  ],
  expert: [
    { value: 'available', label: 'متاح' },
    { value: 'busy', label: 'مشغول' },
    { value: 'unavailable', label: 'غير متاح' }
  ],
  solution: [
    { value: 'draft', label: 'مسودة' },
    { value: 'published', label: 'منشور' },
    { value: 'verified', label: 'موثق' }
  ]
}

const URGENCY_OPTIONS = [
  { value: 'low', label: 'منخفضة' },
  { value: 'medium', label: 'متوسطة' },
  { value: 'high', label: 'عالية' },
  { value: 'critical', label: 'حرجة' }
]

export function SearchFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  availableCategories = [],
  availableSectors = [],
  availableLocations = [],
  className = '',
  onSavePreset,
  onLoadPreset,
  savedPresets = []
}: SearchFiltersProps) {
  const { isMobile, isTablet, isTouchDevice } = useDeviceType()
  const hasTouch = useTouchSupport()
  const [isExpanded, setIsExpanded] = useState(false)
  const [dateFromOpen, setDateFromOpen] = useState(false)
  const [dateToOpen, setDateToOpen] = useState(false)
  const [presetName, setPresetName] = useState('')
  const [showPresetDialog, setShowPresetDialog] = useState(false)

  // Count active filters
  const activeFiltersCount = Object.entries(filters).reduce((count, [key, value]) => {
    if (key === 'dateRange' && value) {
      const dateRange = value as { from?: string; to?: string }
      return count + (dateRange.from ? 1 : 0) + (dateRange.to ? 1 : 0)
    }
    if (Array.isArray(value)) {
      return count + value.length
    }
    if (value && typeof value === 'object') {
      return count + Object.values(value).filter(v => v !== undefined && v !== null).length
    }
    if (value) {
      return count + 1
    }
    return count
  }, 0)

  const updateFilter = <K extends keyof SearchFiltersType>(
    key: K,
    value: SearchFiltersType[K]
  ) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const toggleArrayFilter = <K extends keyof SearchFiltersType>(
    key: K,
    value: string,
    currentArray: string[] = []
  ) => {
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    
    updateFilter(key, newArray as SearchFiltersType[K])
  }

  const getContentTypeIcon = (type: ContentType) => {
    switch (type) {
      case 'problem': return <FileText className="w-4 h-4" />
      case 'expert': return <User className="w-4 h-4" />
      case 'solution': return <Lightbulb className="w-4 h-4" />
      case 'webinar': return <Video className="w-4 h-4" />
    }
  }

  const getContentTypeLabel = (type: ContentType) => {
    switch (type) {
      case 'problem': return 'المشاكل'
      case 'expert': return 'الخبراء'
      case 'solution': return 'الحلول'
      case 'webinar': return 'الندوات'
    }
  }

  const getStatusOptions = () => {
    if (!filters.contentType || filters.contentType.length === 0) {
      return [...STATUS_OPTIONS.problem, ...STATUS_OPTIONS.expert, ...STATUS_OPTIONS.solution]
    }
    
    let options: { value: string; label: string }[] = []
    filters.contentType.forEach(type => {
      if (type in STATUS_OPTIONS) {
        options.push(...STATUS_OPTIONS[type as keyof typeof STATUS_OPTIONS])
      }
    })
    
    return options
  }

  return (
    <Card className={className} role="region" aria-labelledby="filters-title">
      <CardHeader className={`${isMobile ? 'pb-2' : 'pb-3'}`}>
        <div className={`flex items-center justify-between ${isMobile ? 'flex-col gap-2' : ''}`}>
          <CardTitle id="filters-title" className={`${isMobile ? 'text-base' : 'text-lg'} flex items-center gap-2 ${isMobile ? 'w-full justify-center' : ''}`}>
            <Filter className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
            تصفية النتائج
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="mr-2" aria-label={`${activeFiltersCount} فلتر نشط`}>
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          <div className={`flex items-center gap-2 ${isMobile ? 'w-full justify-between' : ''}`}>
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size={isMobile ? "sm" : "sm"}
                onClick={onClearFilters}
                className={`text-red-600 hover:text-red-700 ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                aria-label={`مسح جميع الفلاتر (${activeFiltersCount} فلتر نشط)`}
              >
                <X className="w-4 h-4 mr-1" />
                مسح الكل
              </Button>
            )}
            {/* Filter Presets */}
            {savedPresets.length > 0 && (
              <Select
                value=""
                onValueChange={(value) => {
                  const preset = savedPresets.find(p => p.name === value)
                  if (preset && onLoadPreset) {
                    onLoadPreset(preset.filters)
                  }
                }}
              >
                <SelectTrigger className={`${isMobile ? 'w-full' : 'w-40'} ${isTouchDevice ? 'min-h-[44px]' : ''}`}>
                  <SelectValue placeholder="فلاتر محفوظة" />
                </SelectTrigger>
                <SelectContent>
                  {savedPresets.map(preset => (
                    <SelectItem key={preset.name} value={preset.name}>
                      {preset.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}

            {/* Save Preset Button */}
            {activeFiltersCount > 0 && onSavePreset && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPresetDialog(true)}
                className={`${isTouchDevice ? 'min-h-[44px]' : ''}`}
                aria-label="حفظ الفلاتر الحالية كإعداد مسبق"
              >
                <Tag className="w-4 h-4 mr-1" />
                حفظ
              </Button>
            )}

            {!isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className={`space-y-6 ${isMobile ? 'p-4' : ''}`}>
        {/* Basic Filters - Always Visible */}
        <div className={`grid ${isMobile ? 'grid-cols-1 gap-4' : 'grid-cols-1 md:grid-cols-3 gap-4'}`}>
          {/* Content Type Filter */}
          <fieldset className="space-y-2">
            <legend className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium`}>نوع المحتوى</legend>
            <div className={`${isMobile ? 'grid grid-cols-2 gap-2' : 'space-y-2'}`} role="group" aria-labelledby="content-type-legend">
              {(['problem', 'expert', 'solution', 'webinar'] as ContentType[]).map(type => (
                <div key={type} className={`flex items-center space-x-2 space-x-reverse ${isTouchDevice ? 'min-h-[44px]' : ''}`}>
                  <Checkbox
                    id={`content-${type}`}
                    checked={filters.contentType?.includes(type) || false}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        toggleArrayFilter('contentType', type, filters.contentType || [])
                      } else {
                        toggleArrayFilter('contentType', type, filters.contentType || [])
                      }
                    }}
                    className={isTouchDevice ? 'w-5 h-5' : ''}
                    aria-describedby={`content-${type}-desc`}
                  />
                  <Label 
                    htmlFor={`content-${type}`} 
                    className={`flex items-center gap-2 ${isMobile ? 'text-sm' : 'text-sm'} cursor-pointer`}
                  >
                    {getContentTypeIcon(type)}
                    {getContentTypeLabel(type)}
                  </Label>
                  <span id={`content-${type}-desc`} className="sr-only">
                    {type === 'problem' ? 'فلترة المشاكل التقنية' : 
                     type === 'expert' ? 'فلترة الخبراء والمختصين' : 
                     type === 'solution' ? 'فلترة الحلول والاقتراحات' : 'فلترة الندوات والمحاضرات'}
                  </span>
                </div>
              ))}
            </div>
          </fieldset>

          {/* Sector Filter */}
          <div className="space-y-2">
            <Label className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium`}>القطاع</Label>
            <Select
              value={filters.sectors?.[0] || ''}
              onValueChange={(value) => {
                if (value) {
                  updateFilter('sectors', [value])
                } else {
                  updateFilter('sectors', [])
                }
              }}
            >
              <SelectTrigger className={isTouchDevice ? 'min-h-[44px]' : ''}>
                <SelectValue placeholder="اختر القطاع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">جميع القطاعات</SelectItem>
                {SECTORS.map(sector => (
                  <SelectItem key={sector} value={sector}>
                    {SECTOR_LABELS[sector]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium`}>الحالة</Label>
            <Select
              value={filters.status?.[0] || ''}
              onValueChange={(value) => {
                if (value) {
                  updateFilter('status', [value])
                } else {
                  updateFilter('status', [])
                }
              }}
            >
              <SelectTrigger className={isTouchDevice ? 'min-h-[44px]' : ''}>
                <SelectValue placeholder="اختر الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">جميع الحالات</SelectItem>
                {getStatusOptions().map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Advanced Filters - Collapsible on desktop, always visible on mobile */}
        <Collapsible open={isMobile || isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-6">
            {!isMobile && <Separator />}

            {/* Date Range Filter */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">نطاق التاريخ</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs text-gray-600">من تاريخ</Label>
                  <Popover open={dateFromOpen} onOpenChange={setDateFromOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-right"
                      >
                        <CalendarIcon className="w-4 h-4 ml-2" />
                        {filters.dateRange?.from
                          ? format(new Date(filters.dateRange.from), 'PPP', { locale: ar })
                          : 'اختر التاريخ'
                        }
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={filters.dateRange?.from ? new Date(filters.dateRange.from) : undefined}
                        onSelect={(date) => {
                          updateFilter('dateRange', {
                            ...filters.dateRange,
                            from: date?.toISOString()
                          })
                          setDateFromOpen(false)
                        }}
                        locale={ar}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs text-gray-600">إلى تاريخ</Label>
                  <Popover open={dateToOpen} onOpenChange={setDateToOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-right"
                      >
                        <CalendarIcon className="w-4 h-4 ml-2" />
                        {filters.dateRange?.to
                          ? format(new Date(filters.dateRange.to), 'PPP', { locale: ar })
                          : 'اختر التاريخ'
                        }
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={filters.dateRange?.to ? new Date(filters.dateRange.to) : undefined}
                        onSelect={(date) => {
                          updateFilter('dateRange', {
                            ...filters.dateRange,
                            to: date?.toISOString()
                          })
                          setDateToOpen(false)
                        }}
                        locale={ar}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              
              {(filters.dateRange?.from || filters.dateRange?.to) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilter('dateRange', {})}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="w-3 h-3 mr-1" />
                  مسح التواريخ
                </Button>
              )}
            </div>

            {/* Location Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                الموقع الجغرافي
              </Label>
              <Input
                placeholder="اكتب اسم المحافظة أو المدينة"
                value={filters.location || ''}
                onChange={(e) => updateFilter('location', e.target.value)}
              />
            </div>

            {/* Rating Filter */}
            <div className="space-y-3">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Star className="w-4 h-4" />
                التقييم
              </Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs text-gray-600">الحد الأدنى</Label>
                  <Select
                    value={filters.rating?.min?.toString() || ''}
                    onValueChange={(value) => {
                      updateFilter('rating', {
                        ...filters.rating,
                        min: value ? parseFloat(value) : undefined
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="أي تقييم" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">أي تقييم</SelectItem>
                      <SelectItem value="1">1 نجمة فأكثر</SelectItem>
                      <SelectItem value="2">2 نجمة فأكثر</SelectItem>
                      <SelectItem value="3">3 نجمة فأكثر</SelectItem>
                      <SelectItem value="4">4 نجمة فأكثر</SelectItem>
                      <SelectItem value="4.5">4.5 نجمة فأكثر</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs text-gray-600">الحد الأقصى</Label>
                  <Select
                    value={filters.rating?.max?.toString() || ''}
                    onValueChange={(value) => {
                      updateFilter('rating', {
                        ...filters.rating,
                        max: value ? parseFloat(value) : undefined
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="أي تقييم" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">أي تقييم</SelectItem>
                      <SelectItem value="2">2 نجمة أو أقل</SelectItem>
                      <SelectItem value="3">3 نجمة أو أقل</SelectItem>
                      <SelectItem value="4">4 نجمة أو أقل</SelectItem>
                      <SelectItem value="5">5 نجمة أو أقل</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Urgency Filter (for problems) */}
            {(!filters.contentType || filters.contentType.includes('problem')) && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">مستوى الأولوية</Label>
                <div className="grid grid-cols-2 gap-2">
                  {URGENCY_OPTIONS.map(option => (
                    <div key={option.value} className="flex items-center space-x-2 space-x-reverse">
                      <Checkbox
                        id={`urgency-${option.value}`}
                        checked={filters.urgency?.includes(option.value) || false}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            toggleArrayFilter('urgency', option.value, filters.urgency || [])
                          } else {
                            toggleArrayFilter('urgency', option.value, filters.urgency || [])
                          }
                        }}
                      />
                      <Label 
                        htmlFor={`urgency-${option.value}`} 
                        className="text-sm cursor-pointer"
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Categories Filter */}
            {availableCategories.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">الفئات</Label>
                <Select
                  value={filters.categories?.[0] || ''}
                  onValueChange={(value) => {
                    if (value) {
                      updateFilter('categories', [value])
                    } else {
                      updateFilter('categories', [])
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الفئات</SelectItem>
                    {availableCategories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Tags Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Tag className="w-4 h-4" />
                البحث في العلامات
              </Label>
              <Input
                placeholder="اكتب العلامات مفصولة بفواصل"
                value={filters.tags?.join(', ') || ''}
                onChange={(e) => {
                  const tags = e.target.value
                    .split(',')
                    .map(tag => tag.trim())
                    .filter(tag => tag.length > 0)
                  updateFilter('tags', tags.length > 0 ? tags : undefined)
                }}
              />
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Active Filters Summary */}
        {activeFiltersCount > 0 && (
          <div className="space-y-2">
            <Separator />
            <div className="flex flex-wrap gap-2">
              {filters.contentType?.map(type => (
                <Badge key={type} variant="secondary" className="flex items-center gap-1">
                  {getContentTypeIcon(type)}
                  {getContentTypeLabel(type)}
                  <X 
                    className="w-3 h-3 cursor-pointer hover:text-red-600" 
                    onClick={() => toggleArrayFilter('contentType', type, filters.contentType || [])}
                  />
                </Badge>
              ))}
              
              {filters.sectors?.map(sector => (
                <Badge key={sector} variant="secondary" className="flex items-center gap-1">
                  {SECTOR_LABELS[sector]}
                  <X 
                    className="w-3 h-3 cursor-pointer hover:text-red-600" 
                    onClick={() => toggleArrayFilter('sectors', sector, filters.sectors || [])}
                  />
                </Badge>
              ))}
              
              {filters.status?.map(status => (
                <Badge key={status} variant="secondary" className="flex items-center gap-1">
                  {getStatusOptions().find(opt => opt.value === status)?.label || status}
                  <X 
                    className="w-3 h-3 cursor-pointer hover:text-red-600" 
                    onClick={() => toggleArrayFilter('status', status, filters.status || [])}
                  />
                </Badge>
              ))}
              
              {filters.location && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  {filters.location}
                  <X 
                    className="w-3 h-3 cursor-pointer hover:text-red-600" 
                    onClick={() => updateFilter('location', undefined)}
                  />
                </Badge>
              )}
              
              {filters.rating?.min && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Star className="w-3 h-3" />
                  {filters.rating.min}+ نجمة
                  <X 
                    className="w-3 h-3 cursor-pointer hover:text-red-600" 
                    onClick={() => updateFilter('rating', { ...filters.rating, min: undefined })}
                  />
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}