
import { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/components/auth/AuthProvider";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { AccessibilityProvider, AccessibilityToolbar } from "@/contexts/AccessibilityContext";
import { QueryProvider } from "@/providers/QueryProvider";
import { queryClient } from "@/lib/queryClient";
import { setupCachePersistence, setupCacheCleanup } from "@/lib/cachePersistence";
import { setupNavigationPreloading } from "@/utils/componentPreloader";
import "@/styles/accessibility.css";

// Lazy-loaded components
import {
  Index,
  Experts,
  Submit,
  Problems,
  ProblemDetail,
  ProblemSubmit,
  EntryDetails,
  Profile,
  Login,
  Register,
  ForgotPassword,
  ExpertProfileCreate,
  ExpertDashboardPage,
  ExpertProfile,
  SearchResults,
  Settings,
  AdminPanel,
  AdminUsers,
  AdminAnalytics,
  AdminDashboard,
  SupabaseTest,
  NotFound,
} from "@/routes/lazyRoutes";

// Loading and error handling components
import { LazyLoadingFallback } from "@/components/common/LazyLoadingFallback";
import { LazyLoadErrorBoundary } from "@/components/common/LazyLoadErrorBoundary";
import { RoutePreloader } from "@/components/common/RoutePreloader";
import { RouteErrorBoundary } from "@/components/common/RouteErrorBoundary";
import { ComponentErrorBoundary } from "@/components/common/ComponentErrorBoundary";

// Initialize cache persistence, cleanup, and component preloading
if (typeof window !== 'undefined') {
  setupCachePersistence(queryClient);
  setupCacheCleanup();
  
  // Set up component preloading after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupNavigationPreloading);
  } else {
    setupNavigationPreloading();
  }
}

const App = () => (
  <QueryProvider>
    <AuthProvider>
      <LanguageProvider>
        <AccessibilityProvider>
          <TooltipProvider>
            <AccessibilityToolbar />
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <RouteErrorBoundary
                routeName="app-root"
                enableReporting={true}
                maxRetries={3}
              >
                <LazyLoadErrorBoundary>
                  <RoutePreloader />
                  <Suspense fallback={<LazyLoadingFallback />}>
                    <Routes>
                      <Route path="/" element={
                        <RouteErrorBoundary routeName="home" enableReporting={true}>
                          <Index />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/experts" element={
                        <RouteErrorBoundary routeName="experts" enableReporting={true}>
                          <Experts />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/submit" element={
                        <RouteErrorBoundary routeName="submit" enableReporting={true}>
                          <Submit />
                        </RouteErrorBoundary>
                      } />

                      {/* Problem Management Routes */}
                      <Route path="/problems" element={
                        <RouteErrorBoundary routeName="problems" enableReporting={true}>
                          <Problems />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/problems/new" element={
                        <RouteErrorBoundary routeName="problem-submit" enableReporting={true}>
                          <ProblemSubmit />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/problems/:id" element={
                        <RouteErrorBoundary routeName="problem-detail" enableReporting={true}>
                          <ProblemDetail />
                        </RouteErrorBoundary>
                      } />

                      {/* Expert Routes */}
                      <Route path="/experts/profile/create" element={
                        <RouteErrorBoundary routeName="expert-profile-create" enableReporting={true}>
                          <ExpertProfileCreate />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/experts/dashboard" element={
                        <RouteErrorBoundary routeName="expert-dashboard" enableReporting={true}>
                          <ExpertDashboardPage />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/experts/:id" element={
                        <RouteErrorBoundary routeName="expert-profile" enableReporting={true}>
                          <ExpertProfile />
                        </RouteErrorBoundary>
                      } />

                      {/* Search Routes */}
                      <Route path="/search" element={
                        <RouteErrorBoundary routeName="search" enableReporting={true}>
                          <SearchResults />
                        </RouteErrorBoundary>
                      } />

                      <Route path="/entry/:id" element={
                        <RouteErrorBoundary routeName="entry-details" enableReporting={true}>
                          <EntryDetails />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/profile" element={
                        <RouteErrorBoundary routeName="profile" enableReporting={true}>
                          <Profile />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/settings" element={
                        <RouteErrorBoundary routeName="settings" enableReporting={true}>
                          <Settings />
                        </RouteErrorBoundary>
                      } />

                      {/* Admin Routes */}
                      <Route path="/admin" element={
                        <RouteErrorBoundary routeName="admin" enableReporting={true}>
                          <AdminPanel />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/admin/dashboard" element={
                        <RouteErrorBoundary routeName="admin-dashboard" enableReporting={true}>
                          <AdminDashboard />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/admin/users" element={
                        <RouteErrorBoundary routeName="admin-users" enableReporting={true}>
                          <AdminUsers />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/admin/analytics" element={
                        <RouteErrorBoundary routeName="admin-analytics" enableReporting={true}>
                          <AdminAnalytics />
                        </RouteErrorBoundary>
                      } />

                      {/* Authentication Routes */}
                      <Route path="/auth/login" element={
                        <RouteErrorBoundary routeName="login" enableReporting={true}>
                          <Login />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/auth/register" element={
                        <RouteErrorBoundary routeName="register" enableReporting={true}>
                          <Register />
                        </RouteErrorBoundary>
                      } />
                      <Route path="/auth/forgot-password" element={
                        <RouteErrorBoundary routeName="forgot-password" enableReporting={true}>
                          <ForgotPassword />
                        </RouteErrorBoundary>
                      } />

                      {/* Development/Testing Routes */}
                      <Route path="/supabase-test" element={
                        <RouteErrorBoundary routeName="supabase-test" enableReporting={false}>
                          <SupabaseTest />
                        </RouteErrorBoundary>
                      } />

                      {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                      <Route path="*" element={
                        <RouteErrorBoundary routeName="not-found" enableReporting={false}>
                          <NotFound />
                        </RouteErrorBoundary>
                      } />
                    </Routes>
                  </Suspense>
                </LazyLoadErrorBoundary>
              </RouteErrorBoundary>
            </BrowserRouter>
          </TooltipProvider>
        </AccessibilityProvider>
      </LanguageProvider>
    </AuthProvider>
  </QueryProvider>
);

export default App;
