
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/components/auth/AuthProvider";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { AccessibilityProvider, AccessibilityToolbar } from "@/contexts/AccessibilityContext";
import "@/styles/accessibility.css";
import { SupabaseTest } from "@/components/SupabaseTest";
import Index from "./pages/Index";
import Experts from "./pages/Experts";
import Submit from "./pages/Submit";
import Problems from "./pages/Problems";
import ProblemDetail from "./pages/ProblemDetail";
import ProblemSubmit from "./pages/ProblemSubmit";
import EntryDetails from "./pages/EntryDetails";
import Profile from "./pages/Profile";
import Login from "./pages/auth/Login";
import Register from "./pages/auth/Register";
import ForgotPassword from "./pages/auth/ForgotPassword";
import ExpertProfileCreate from "./pages/ExpertProfileCreate";
import ExpertDashboardPage from "./pages/ExpertDashboardPage";
import ExpertProfile from "./pages/ExpertProfile";
import SearchResults from "./pages/SearchResults";
import Settings from "./pages/Settings";
import AdminPanel from "./pages/AdminPanel";
import AdminUsers from "./pages/AdminUsers";
import AdminAnalytics from "./pages/AdminAnalytics";
import AdminDashboard from "./pages/admin/AdminDashboard";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <LanguageProvider>
        <AccessibilityProvider>
          <TooltipProvider>
            <AccessibilityToolbar />
            <Toaster />
            <Sonner />
            <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/experts" element={<Experts />} />
              <Route path="/submit" element={<Submit />} />
              
              {/* Problem Management Routes */}
              <Route path="/problems" element={<Problems />} />
              <Route path="/problems/new" element={<ProblemSubmit />} />
              <Route path="/problems/:id" element={<ProblemDetail />} />
              
              {/* Expert Routes */}
              <Route path="/experts/profile/create" element={<ExpertProfileCreate />} />
              <Route path="/experts/dashboard" element={<ExpertDashboardPage />} />
              <Route path="/experts/:id" element={<ExpertProfile />} />
              
              {/* Search Routes */}
              <Route path="/search" element={<SearchResults />} />
              
              <Route path="/entry/:id" element={<EntryDetails />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/settings" element={<Settings />} />
              
              {/* Admin Routes */}
              <Route path="/admin" element={<AdminPanel />} />
              <Route path="/admin/dashboard" element={<AdminDashboard />} />
              <Route path="/admin/users" element={<AdminUsers />} />
              <Route path="/admin/analytics" element={<AdminAnalytics />} />
              
              {/* Authentication Routes */}
              <Route path="/auth/login" element={<Login />} />
              <Route path="/auth/register" element={<Register />} />
              <Route path="/auth/forgot-password" element={<ForgotPassword />} />
              
              {/* Development/Testing Routes */}
              <Route path="/supabase-test" element={<SupabaseTest />} />
              
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
          </TooltipProvider>
        </AccessibilityProvider>
      </LanguageProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
