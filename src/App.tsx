
import { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/components/auth/AuthProvider";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { AccessibilityProvider, AccessibilityToolbar } from "@/contexts/AccessibilityContext";
import { QueryProvider } from "@/providers/QueryProvider";
import { queryClient } from "@/lib/queryClient";
import { setupCachePersistence, setupCacheCleanup } from "@/lib/cachePersistence";
import { setupNavigationPreloading } from "@/utils/componentPreloader";
import "@/styles/accessibility.css";

// Lazy-loaded components
import {
  Index,
  Experts,
  Submit,
  Problems,
  ProblemDetail,
  ProblemSubmit,
  EntryDetails,
  Profile,
  Login,
  Register,
  ForgotPassword,
  ExpertProfileCreate,
  ExpertDashboardPage,
  ExpertProfile,
  SearchResults,
  Settings,
  AdminPanel,
  AdminUsers,
  AdminAnalytics,
  AdminDashboard,
  SupabaseTest,
  NotFound,
} from "@/routes/lazyRoutes";

// Loading and error handling components
import { LazyLoadingFallback } from "@/components/common/LazyLoadingFallback";
import { LazyLoadErrorBoundary } from "@/components/common/LazyLoadErrorBoundary";
import { RoutePreloader } from "@/components/common/RoutePreloader";

// Initialize cache persistence, cleanup, and component preloading
if (typeof window !== 'undefined') {
  setupCachePersistence(queryClient);
  setupCacheCleanup();
  
  // Set up component preloading after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupNavigationPreloading);
  } else {
    setupNavigationPreloading();
  }
}

const App = () => (
  <QueryProvider>
    <AuthProvider>
      <LanguageProvider>
        <AccessibilityProvider>
          <TooltipProvider>
            <AccessibilityToolbar />
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <LazyLoadErrorBoundary>
                <RoutePreloader />
                <Suspense fallback={<LazyLoadingFallback />}>
                  <Routes>
                    <Route path="/" element={<Index />} />
                    <Route path="/experts" element={<Experts />} />
                    <Route path="/submit" element={<Submit />} />
                    
                    {/* Problem Management Routes */}
                    <Route path="/problems" element={<Problems />} />
                    <Route path="/problems/new" element={<ProblemSubmit />} />
                    <Route path="/problems/:id" element={<ProblemDetail />} />
                    
                    {/* Expert Routes */}
                    <Route path="/experts/profile/create" element={<ExpertProfileCreate />} />
                    <Route path="/experts/dashboard" element={<ExpertDashboardPage />} />
                    <Route path="/experts/:id" element={<ExpertProfile />} />
                    
                    {/* Search Routes */}
                    <Route path="/search" element={<SearchResults />} />
                    
                    <Route path="/entry/:id" element={<EntryDetails />} />
                    <Route path="/profile" element={<Profile />} />
                    <Route path="/settings" element={<Settings />} />
                    
                    {/* Admin Routes */}
                    <Route path="/admin" element={<AdminPanel />} />
                    <Route path="/admin/dashboard" element={<AdminDashboard />} />
                    <Route path="/admin/users" element={<AdminUsers />} />
                    <Route path="/admin/analytics" element={<AdminAnalytics />} />
                    
                    {/* Authentication Routes */}
                    <Route path="/auth/login" element={<Login />} />
                    <Route path="/auth/register" element={<Register />} />
                    <Route path="/auth/forgot-password" element={<ForgotPassword />} />
                    
                    {/* Development/Testing Routes */}
                    <Route path="/supabase-test" element={<SupabaseTest />} />
                    
                    {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </Suspense>
              </LazyLoadErrorBoundary>
            </BrowserRouter>
          </TooltipProvider>
        </AccessibilityProvider>
      </LanguageProvider>
    </AuthProvider>
  </QueryProvider>
);

export default App;
