import React from 'react';
import { ComponentErrorBoundary, withComponentErrorBoundary } from '@/components/common/ComponentErrorBoundary';
import { RouteErrorBoundary, withRouteErrorBoundary } from '@/components/common/RouteErrorBoundary';

/**
 * Configuration for error boundary wrapping
 */
interface ErrorBoundaryConfig {
  componentName?: string;
  enableReporting?: boolean;
  maxRetries?: number;
  variant?: 'component' | 'inline';
  isolateError?: boolean;
  routeName?: string;
}

/**
 * Wrap a component with appropriate error boundary based on its type
 */
export function wrapWithErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  config: ErrorBoundaryConfig = {}
) {
  const {
    componentName = Component.displayName || Component.name,
    enableReporting = true,
    maxRetries = 3,
    variant = 'component',
    isolateError = true,
    routeName,
  } = config;

  if (routeName) {
    // This is a route component
    return withRouteErrorBoundary(Component, {
      routeName,
      enableReporting,
      maxRetries,
    });
  } else {
    // This is a regular component
    return withComponentErrorBoundary(Component, {
      componentName,
      enableReporting,
      maxRetries,
      variant,
      isolateError,
    });
  }
}

/**
 * Decorator for class components
 */
export function ErrorBoundaryDecorator(config: ErrorBoundaryConfig = {}) {
  return function <T extends React.ComponentType<any>>(target: T): T {
    return wrapWithErrorBoundary(target, config) as T;
  };
}

/**
 * Hook to add error boundary to functional components
 */
export function useErrorBoundary(componentName?: string) {
  const [error, setError] = React.useState<Error | null>(null);

  const reportError = React.useCallback((error: Error, context?: Record<string, any>) => {
    console.error(`Error in ${componentName}:`, error);
    setError(error);
  }, [componentName]);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  // Throw error to be caught by error boundary
  if (error) {
    throw error;
  }

  return {
    reportError,
    clearError,
  };
}

/**
 * Higher-order component for critical components that should never fail
 */
export function withCriticalErrorHandling<P extends object>(
  Component: React.ComponentType<P>,
  fallbackComponent?: React.ComponentType<P>
) {
  return React.forwardRef<any, P>((props, ref) => {
    const [hasError, setHasError] = React.useState(false);

    React.useEffect(() => {
      const handleError = (event: ErrorEvent) => {
        console.error('Critical component error:', event.error);
        setHasError(true);
      };

      window.addEventListener('error', handleError);
      return () => window.removeEventListener('error', handleError);
    }, []);

    if (hasError && fallbackComponent) {
      const FallbackComponent = fallbackComponent;
      return React.createElement(FallbackComponent, props);
    }

    return (
      <ComponentErrorBoundary
        componentName={Component.displayName || Component.name}
        enableReporting={true}
        maxRetries={1}
        isolateError={false}
      >
        <Component {...props} ref={ref} />
      </ComponentErrorBoundary>
    );
  });
}

/**
 * Async error handler for promises and async operations
 */
export class AsyncErrorHandler {
  private static instance: AsyncErrorHandler;
  private errorHandlers: Map<string, (error: Error) => void> = new Map();

  static getInstance(): AsyncErrorHandler {
    if (!AsyncErrorHandler.instance) {
      AsyncErrorHandler.instance = new AsyncErrorHandler();
    }
    return AsyncErrorHandler.instance;
  }

  registerHandler(key: string, handler: (error: Error) => void) {
    this.errorHandlers.set(key, handler);
  }

  unregisterHandler(key: string) {
    this.errorHandlers.delete(key);
  }

  async handleAsync<T>(
    operation: () => Promise<T>,
    context: string = 'unknown'
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      
      // Try to find a specific handler
      const handler = this.errorHandlers.get(context);
      if (handler) {
        handler(err);
      } else {
        // Default error handling
        console.error(`Async error in ${context}:`, err);
      }
      
      return null;
    }
  }
}

/**
 * Hook for handling async errors
 */
export function useAsyncErrorHandler(context: string = 'component') {
  const handler = React.useMemo(() => AsyncErrorHandler.getInstance(), []);

  const handleAsync = React.useCallback(
    async <T>(operation: () => Promise<T>): Promise<T | null> => {
      return handler.handleAsync(operation, context);
    },
    [handler, context]
  );

  const registerErrorHandler = React.useCallback(
    (errorHandler: (error: Error) => void) => {
      handler.registerHandler(context, errorHandler);
      
      return () => {
        handler.unregisterHandler(context);
      };
    },
    [handler, context]
  );

  return {
    handleAsync,
    registerErrorHandler,
  };
}

/**
 * Error boundary for specific UI sections
 */
export function SectionErrorBoundary({
  children,
  sectionName,
  fallback,
}: {
  children: React.ReactNode;
  sectionName: string;
  fallback?: React.ReactNode;
}) {
  return (
    <ComponentErrorBoundary
      componentName={`Section-${sectionName}`}
      enableReporting={true}
      variant="inline"
      isolateError={true}
      fallback={fallback ? () => fallback : undefined}
    >
      {children}
    </ComponentErrorBoundary>
  );
}

/**
 * Error boundary for form components
 */
export function FormErrorBoundary({
  children,
  formName,
}: {
  children: React.ReactNode;
  formName: string;
}) {
  return (
    <ComponentErrorBoundary
      componentName={`Form-${formName}`}
      enableReporting={true}
      variant="component"
      isolateError={true}
      maxRetries={2}
    >
      {children}
    </ComponentErrorBoundary>
  );
}

/**
 * Error boundary for data display components
 */
export function DataErrorBoundary({
  children,
  dataType,
}: {
  children: React.ReactNode;
  dataType: string;
}) {
  return (
    <ComponentErrorBoundary
      componentName={`Data-${dataType}`}
      enableReporting={true}
      variant="inline"
      isolateError={true}
      maxRetries={1}
    >
      {children}
    </ComponentErrorBoundary>
  );
}

/**
 * Utility to create error-safe versions of components
 */
export function createErrorSafeComponent<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    name?: string;
    fallback?: React.ComponentType<P>;
    enableReporting?: boolean;
  } = {}
) {
  const {
    name = Component.displayName || Component.name,
    fallback,
    enableReporting = true,
  } = options;

  return React.forwardRef<any, P>((props, ref) => (
    <ComponentErrorBoundary
      componentName={name}
      enableReporting={enableReporting}
      variant="component"
      isolateError={true}
      fallback={fallback ? ({ resetError }) => (
        <fallback {...props} ref={ref} />
      ) : undefined}
    >
      <Component {...props} ref={ref} />
    </ComponentErrorBoundary>
  ));
}
