import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'ar' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Translation dictionaries
const translations = {
  ar: {
    // Header Navigation
    'nav.home': 'الرئيسية',
    'nav.problems': 'المشاكل',
    'nav.experts': 'الخبراء',
    'nav.submit_problem': 'إرسال مشكلة',
    'nav.search': 'البحث',
    'nav.admin_panel': 'لوحة الإدارة',
    'nav.profile': 'الملف الشخصي',
    'nav.settings': 'الإعدادات',
    'nav.user_management': 'إدارة المستخدمين',
    'nav.analytics': 'التحليلات',
    'nav.logout': 'تسجيل الخروج',
    'nav.login': 'تسجيل الدخول',
    'nav.register': 'إنشاء حساب',
    'nav.menu': 'القائمة',

    // Brand
    'brand.title': 'مركز سوريا الذكي',
    'brand.subtitle': 'منصة الحلول التقنية',

    // User Roles
    'role.admin': 'مدير',
    'role.expert': 'خبير',
    'role.ministry_user': 'موظف وزارة',

    // Problems Page
    'problems.title': 'المشاكل التقنية',
    'problems.subtitle': 'تصفح وابحث في المشاكل التقنية المطروحة من قبل الوزارات والمؤسسات',
    'problems.add_problem': 'إضافة مشكلة',
    'problems.search_filter': 'البحث والتصفية',
    'problems.search_placeholder': 'ابحث في العناوين، الأوصاف، أو الكلمات المفتاحية...',
    'problems.status': 'الحالة',
    'problems.priority': 'الأولوية',
    'problems.category': 'الفئة التقنية',
    'problems.sector': 'القطاع',
    'problems.sort': 'الترتيب',
    'problems.all_statuses': 'جميع الحالات',
    'problems.all_priorities': 'جميع الأولويات',
    'problems.all_categories': 'جميع الفئات',
    'problems.all_sectors': 'جميع القطاعات',
    'problems.newest_first': 'الأحدث أولاً',
    'problems.oldest_first': 'الأقدم أولاً',
    'problems.title_az': 'العنوان (أ-ي)',
    'problems.title_za': 'العنوان (ي-أ)',
    'problems.active_filters': 'المرشحات النشطة',
    'problems.clear_filters': 'مسح جميع المرشحات',
    'problems.loading': 'جاري التحميل...',
    'problems.no_problems': 'لا توجد مشاكل',
    'problems.no_results': 'لا توجد مشاكل تطابق معايير البحث المحددة',
    'problems.no_problems_yet': 'لا توجد مشاكل مطروحة حالياً',
    'problems.add_new_problem': 'إضافة مشكلة جديدة',
    'problems.by': 'بواسطة',
    'problems.solutions_count': 'حل',
    'problems.attachment': 'مرفق',
    'problems.attachments': 'مرفقات',

    // Problem Status
    'status.open': 'مفتوحة',
    'status.in_progress': 'قيد المعالجة',
    'status.resolved': 'محلولة',
    'status.closed': 'مغلقة',

    // Priority Levels
    'priority.low': 'منخفضة',
    'priority.medium': 'متوسطة',
    'priority.high': 'عالية',
    'priority.critical': 'حرجة',

    // Problem Submission
    'submit.title': 'إرسال مشكلة تقنية جديدة',
    'submit.subtitle': 'اشرح المشكلة التقنية التي تواجهها بالتفصيل ليتمكن الخبراء من تقديم أفضل الحلول',
    'submit.problem_title': 'عنوان المشكلة',
    'submit.problem_title_required': 'عنوان المشكلة *',
    'submit.problem_description': 'وصف تفصيلي للمشكلة',
    'submit.problem_description_required': 'وصف تفصيلي للمشكلة *',
    'submit.technical_category': 'الفئة التقنية',
    'submit.technical_category_required': 'الفئة التقنية *',
    'submit.sector_required': 'القطاع *',
    'submit.priority_level': 'مستوى الأولوية',
    'submit.keywords': 'الكلمات المفتاحية (اختياري)',
    'submit.attachments': 'المرفقات (اختياري)',
    'submit.cancel': 'إلغاء',
    'submit.submit_problem': 'إرسال المشكلة',
    'submit.submitting': 'جاري الإرسال...',
    'submit.add_keyword': 'إضافة',
    'submit.keyword_placeholder': 'أضف كلمة مفتاحية',
    'submit.keywords_count': 'كلمات مفتاحية',
    'submit.min_chars': 'الحد الأدنى',
    'submit.chars': 'حرف',

    // Expert Directory
    'experts.title': 'الخبراء المتاحون',
    'experts.advanced_filter': 'فلترة متقدمة',
    'experts.search_placeholder': 'ابحث عن خبير بالاسم، المهارات، أو التخصص...',
    'experts.sort_by': 'ترتيب حسب',
    'experts.rating': 'التقييم',
    'experts.contributions': 'عدد المساهمات',
    'experts.response_time': 'وقت الاستجابة',
    'experts.experience': 'سنوات الخبرة',
    'experts.no_results': 'لا توجد نتائج',
    'experts.no_experts_found': 'لم يتم العثور على خبراء يطابقون معايير البحث الحالية',
    'experts.clear_filters': 'مسح الفلاتر',
    'experts.view_profile': 'عرض الملف الشخصي',
    'experts.expertise_areas': 'مجالات الخبرة',
    'experts.main_skills': 'المهارات الرئيسية',
    'experts.years_experience': 'سنة خبرة',
    'experts.hours': 'ساعة',
    'experts.contributions_count': 'مساهمة',
    'experts.joined': 'انضم في',

    // Availability Status
    'availability.available': 'متاح',
    'availability.busy': 'مشغول',
    'availability.unavailable': 'غير متاح',

    // Common
    'common.loading': 'جاري التحميل...',
    'common.error': 'خطأ',
    'common.success': 'نجح',
    'common.cancel': 'إلغاء',
    'common.save': 'حفظ',
    'common.edit': 'تعديل',
    'common.delete': 'حذف',
    'common.view': 'عرض',
    'common.back': 'العودة',
    'common.next': 'التالي',
    'common.previous': 'السابق',
    'common.close': 'إغلاق',
    'common.confirm': 'تأكيد',
    'common.yes': 'نعم',
    'common.no': 'لا',
    'common.required': 'مطلوب',
    'common.optional': 'اختياري',

    // Time
    'time.now': 'الآن',
    'time.minute_ago': 'منذ دقيقة',
    'time.minutes_ago': 'منذ {count} دقيقة',
    'time.hour_ago': 'منذ ساعة',
    'time.hours_ago': 'ساعة مضت',
    'time.day_ago': 'منذ يوم',
    'time.days_ago': 'يوم مضى',
    'time.week_ago': 'منذ أسبوع',
    'time.weeks_ago': 'أسبوع مضى',
    'time.month_ago': 'منذ شهر',
    'time.months_ago': 'منذ {count} شهر',
    'time.year_ago': 'منذ سنة',
    'time.years_ago': 'منذ {count} سنة',

    // Homepage
    'homepage.hero_title': 'نجمع الخبراء السوريين لحل التحديات التقنية',
    'homepage.hero_subtitle': 'منصة شاملة تربط بين المشاكل التقنية والحلول المبتكرة من خلال شبكة خبراء محليين وعالميين',
    'homepage.stats.problems_solved': 'المشاكل المحلولة',
    'homepage.stats.registered_experts': 'الخبراء المسجلين',
    'homepage.stats.presentations': 'العروض التقديمية',
    'homepage.stats.qa': 'الأسئلة والأجوبة',
    'homepage.browse_by_category': 'تصفح حسب الفئة',
    'homepage.entries_available': 'مدخلة متاحة',
    'homepage.browse_now': 'تصفح الآن',
    'homepage.latest_contributions': 'أحدث المساهمات',
    'homepage.view_all': 'عرض الكل',
    'homepage.view_details': 'عرض التفاصيل',
    'homepage.cta_title': 'شارك خبرتك وساعد في حل التحديات التقنية',
    'homepage.cta_subtitle': 'انضم إلى مجتمع الخبراء السوريين وساهم في بناء مستقبل تقني أفضل',
    'homepage.view_experts': 'عرض الخبراء',
    'homepage.browse_problems': 'تصفح المشاكل',
    'homepage.submit_problem': 'اطرح مشكلة',
    'homepage.join_expert': 'انضم كخبير',
    'homepage.footer.title': 'مركز سوريا الذكي',
    'homepage.footer.description': 'منصة تجمع الخبراء والحلول التقنية لخدمة المجتمع السوري',
    'homepage.footer.quick_links': 'روابط سريعة',
    'homepage.footer.experts': 'الخبراء',
    'homepage.footer.problems': 'المشاكل',
    'homepage.footer.solutions': 'الحلول',
    'homepage.footer.seminars': 'الندوات',
    'homepage.footer.sectors': 'القطاعات',
    'homepage.footer.health': 'الصحة',
    'homepage.footer.education': 'التعليم',
    'homepage.footer.industry': 'الصناعة',
    'homepage.footer.agriculture': 'الزراعة',
    'homepage.footer.contact': 'تواصل معنا',
    'homepage.footer.copyright': '© 2024 مركز سوريا الذكي. جميع الحقوق محفوظة.',

    // Categories
    'categories.ministries': 'الوزارات',
    'categories.industry': 'القطاعات الصناعية',
    'categories.tech': 'التقنيات',
    'categories.education': 'التعليم',
  },
  en: {
    // Header Navigation
    'nav.home': 'Home',
    'nav.problems': 'Problems',
    'nav.experts': 'Experts',
    'nav.submit_problem': 'Submit Problem',
    'nav.search': 'Search',
    'nav.admin_panel': 'Admin Panel',
    'nav.profile': 'Profile',
    'nav.settings': 'Settings',
    'nav.user_management': 'User Management',
    'nav.analytics': 'Analytics',
    'nav.logout': 'Logout',
    'nav.login': 'Login',
    'nav.register': 'Register',
    'nav.menu': 'Menu',

    // Brand
    'brand.title': 'Syria Smart Center',
    'brand.subtitle': 'Technical Solutions Platform',

    // User Roles
    'role.admin': 'Administrator',
    'role.expert': 'Expert',
    'role.ministry_user': 'Ministry Employee',

    // Problems Page
    'problems.title': 'Technical Problems',
    'problems.subtitle': 'Browse and search technical problems submitted by ministries and institutions',
    'problems.add_problem': 'Add Problem',
    'problems.search_filter': 'Search and Filter',
    'problems.search_placeholder': 'Search in titles, descriptions, or keywords...',
    'problems.status': 'Status',
    'problems.priority': 'Priority',
    'problems.category': 'Technical Category',
    'problems.sector': 'Sector',
    'problems.sort': 'Sort',
    'problems.all_statuses': 'All Statuses',
    'problems.all_priorities': 'All Priorities',
    'problems.all_categories': 'All Categories',
    'problems.all_sectors': 'All Sectors',
    'problems.newest_first': 'Newest First',
    'problems.oldest_first': 'Oldest First',
    'problems.title_az': 'Title (A-Z)',
    'problems.title_za': 'Title (Z-A)',
    'problems.active_filters': 'Active Filters',
    'problems.clear_filters': 'Clear All Filters',
    'problems.loading': 'Loading...',
    'problems.no_problems': 'No Problems',
    'problems.no_results': 'No problems match the current search criteria',
    'problems.no_problems_yet': 'No problems have been submitted yet',
    'problems.add_new_problem': 'Add New Problem',
    'problems.by': 'by',
    'problems.solutions_count': 'solution',
    'problems.attachment': 'attachment',
    'problems.attachments': 'attachments',

    // Problem Status
    'status.open': 'Open',
    'status.in_progress': 'In Progress',
    'status.resolved': 'Resolved',
    'status.closed': 'Closed',

    // Priority Levels
    'priority.low': 'Low',
    'priority.medium': 'Medium',
    'priority.high': 'High',
    'priority.critical': 'Critical',

    // Problem Submission
    'submit.title': 'Submit New Technical Problem',
    'submit.subtitle': 'Describe your technical problem in detail so experts can provide the best solutions',
    'submit.problem_title': 'Problem Title',
    'submit.problem_title_required': 'Problem Title *',
    'submit.problem_description': 'Detailed Problem Description',
    'submit.problem_description_required': 'Detailed Problem Description *',
    'submit.technical_category': 'Technical Category',
    'submit.technical_category_required': 'Technical Category *',
    'submit.sector_required': 'Sector *',
    'submit.priority_level': 'Priority Level',
    'submit.keywords': 'Keywords (Optional)',
    'submit.attachments': 'Attachments (Optional)',
    'submit.cancel': 'Cancel',
    'submit.submit_problem': 'Submit Problem',
    'submit.submitting': 'Submitting...',
    'submit.add_keyword': 'Add',
    'submit.keyword_placeholder': 'Add keyword',
    'submit.keywords_count': 'keywords',
    'submit.min_chars': 'minimum',
    'submit.chars': 'characters',

    // Expert Directory
    'experts.title': 'Available Experts',
    'experts.advanced_filter': 'Advanced Filter',
    'experts.search_placeholder': 'Search for expert by name, skills, or specialization...',
    'experts.sort_by': 'Sort by',
    'experts.rating': 'Rating',
    'experts.contributions': 'Contributions',
    'experts.response_time': 'Response Time',
    'experts.experience': 'Years of Experience',
    'experts.no_results': 'No Results',
    'experts.no_experts_found': 'No experts found matching the current search criteria',
    'experts.clear_filters': 'Clear Filters',
    'experts.view_profile': 'View Profile',
    'experts.expertise_areas': 'Expertise Areas',
    'experts.main_skills': 'Main Skills',
    'experts.years_experience': 'years experience',
    'experts.hours': 'hours',
    'experts.contributions_count': 'contributions',
    'experts.joined': 'Joined',

    // Availability Status
    'availability.available': 'Available',
    'availability.busy': 'Busy',
    'availability.unavailable': 'Unavailable',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.view': 'View',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.close': 'Close',
    'common.confirm': 'Confirm',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.required': 'Required',
    'common.optional': 'Optional',

    // Time
    'time.now': 'now',
    'time.minute_ago': 'a minute ago',
    'time.minutes_ago': '{count} minutes ago',
    'time.hour_ago': 'an hour ago',
    'time.hours_ago': 'hours ago',
    'time.day_ago': 'a day ago',
    'time.days_ago': 'days ago',
    'time.week_ago': 'a week ago',
    'time.weeks_ago': 'weeks ago',
    'time.month_ago': 'a month ago',
    'time.months_ago': '{count} months ago',
    'time.year_ago': 'a year ago',
    'time.years_ago': '{count} years ago',

    // Homepage
    'homepage.hero_title': 'Connecting Syrian Experts to Solve Technical Challenges',
    'homepage.hero_subtitle': 'A comprehensive platform linking technical problems with innovative solutions through a network of local and global experts',
    'homepage.stats.problems_solved': 'Problems Solved',
    'homepage.stats.registered_experts': 'Registered Experts',
    'homepage.stats.presentations': 'Presentations',
    'homepage.stats.qa': 'Q&A',
    'homepage.browse_by_category': 'Browse by Category',
    'homepage.entries_available': 'entries available',
    'homepage.browse_now': 'Browse Now',
    'homepage.latest_contributions': 'Latest Contributions',
    'homepage.view_all': 'View All',
    'homepage.view_details': 'View Details',
    'homepage.cta_title': 'Share Your Expertise and Help Solve Technical Challenges',
    'homepage.cta_subtitle': 'Join the Syrian experts community and contribute to building a better technical future',
    'homepage.view_experts': 'View Experts',
    'homepage.browse_problems': 'Browse Problems',
    'homepage.submit_problem': 'Submit Problem',
    'homepage.join_expert': 'Join as Expert',
    'homepage.footer.title': 'Syria Smart Center',
    'homepage.footer.description': 'A platform that brings together experts and technical solutions to serve the Syrian community',
    'homepage.footer.quick_links': 'Quick Links',
    'homepage.footer.experts': 'Experts',
    'homepage.footer.problems': 'Problems',
    'homepage.footer.solutions': 'Solutions',
    'homepage.footer.seminars': 'Seminars',
    'homepage.footer.sectors': 'Sectors',
    'homepage.footer.health': 'Health',
    'homepage.footer.education': 'Education',
    'homepage.footer.industry': 'Industry',
    'homepage.footer.agriculture': 'Agriculture',
    'homepage.footer.contact': 'Contact Us',
    'homepage.footer.copyright': '© 2024 Syria Smart Center. All rights reserved.',

    // Categories
    'categories.ministries': 'Ministries',
    'categories.industry': 'Industrial Sectors',
    'categories.tech': 'Technologies',
    'categories.education': 'Education',
  }
};

interface LanguageProviderProps {
  children: ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>(() => {
    // Get saved language from localStorage or default to Arabic
    const saved = localStorage.getItem('language') as Language;
    return saved || 'ar';
  });

  const isRTL = language === 'ar';

  useEffect(() => {
    // Save language preference
    localStorage.setItem('language', language);
    
    // Update document direction and language
    document.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
    
    // Update body class for styling
    document.body.classList.toggle('rtl', isRTL);
    document.body.classList.toggle('ltr', !isRTL);
  }, [language, isRTL]);

  const t = (key: string, params?: Record<string, string | number>): string => {
    let translation = translations[language][key as keyof typeof translations[typeof language]] || key;
    
    // Handle parameter substitution
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(`{${param}}`, String(value));
      });
    }
    
    return translation;
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    isRTL
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}